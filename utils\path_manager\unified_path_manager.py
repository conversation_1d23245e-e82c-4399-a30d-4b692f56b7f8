#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一路径管理器 - PathManager

提供系统级别的统一路径管理功能，解决路径不一致问题。
采用单例模式，确保全局路径管理的一致性。

核心功能：
1. 统一的路径构建和验证
2. 智能路径检测和修复
3. 路径缓存和性能优化
4. 配置管理和环境变量支持

版本: v1.0
作者: Augment AI
日期: 2025-07-22
"""

import os
import threading
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path

from utils.logger import get_unified_logger, LogTarget

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


class PathManagerError(Exception):
    """路径管理器异常"""
    pass


class PathManager:
    """
    统一路径管理器 - 单例模式
    
    负责系统中所有路径相关的操作，确保路径构建的一致性和正确性。
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(PathManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化路径管理器"""
        if self._initialized:
            return
            
        logger.info("初始化统一路径管理器")
        
        # 配置管理
        self._data_root = None
        self._config = {}
        
        # 路径缓存
        self._path_cache = {}
        self._cache_lock = threading.Lock()

        # 解析缓存
        self._parse_cache = {}
        self._parse_cache_lock = threading.Lock()
        
        # 路径规范配置
        self._path_rules = {
            'case_sensitive': False,  # 是否区分大小写
            'normalize_case': 'preserve',  # 大小写处理：preserve/lower/upper
            'separator': os.sep,  # 路径分隔符
            'validate_paths': True,  # 是否验证路径
        }
        
        # 支持的数据周期
        self._supported_periods = ["tick", "1m", "5m", "15m", "30m", "1h", "1d", "1w", "1mon"]
        
        # 分区规则配置
        self._partition_rules = {
            'tick': {
                'pattern': '{data_root}/{market}/{code}/tick/{year}/{month:02d}/{day:02d}.parquet',
                'description': 'tick数据按日分区'
            },
            'default': {
                'pattern': '{data_root}/{market}/{code}/{period}/{year}.parquet',
                'description': '其他周期按年分区'
            }
        }
        
        # 初始化配置
        self._load_config()
        self._initialized = True
        
        logger.info("统一路径管理器初始化完成")
    
    def _load_config(self):
        """加载配置"""
        try:
            # 从环境变量获取数据根目录
            self._data_root = os.environ.get("QUANT_DATA_ROOT")
            if not self._data_root:
                self._data_root = os.environ.get("DATA_ROOT")
            if not self._data_root:
                # 默认数据根目录
                self._data_root = "D:\\data"
            
            logger.info(f"数据根目录: {self._data_root}")
            
            # 确保数据根目录存在
            os.makedirs(self._data_root, exist_ok=True)
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise PathManagerError(f"配置加载失败: {e}")
    
    def get_data_root(self) -> str:
        """获取数据根目录"""
        return self._data_root
    
    def set_data_root(self, data_root: str):
        """设置数据根目录"""
        if not data_root:
            raise PathManagerError("数据根目录不能为空")
        
        self._data_root = os.path.abspath(data_root)
        os.makedirs(self._data_root, exist_ok=True)
        
        # 清空路径缓存
        with self._cache_lock:
            self._path_cache.clear()

        # 清空解析缓存
        with self._parse_cache_lock:
            self._parse_cache.clear()
        
        logger.info(f"数据根目录已更新: {self._data_root}")
    
    def parse_symbol(self, symbol: str) -> Tuple[str, str]:
        """
        解析股票代码，获取代码和市场（带缓存优化）

        Args:
            symbol: 股票代码，如 "rb00.SF" 或 "000001.SZ"

        Returns:
            Tuple[str, str]: (代码, 市场) 元组

        Raises:
            PathManagerError: 如果股票代码格式不正确
        """
        # 检查缓存
        with self._parse_cache_lock:
            if symbol in self._parse_cache:
                return self._parse_cache[symbol]

        if not isinstance(symbol, str) or '.' not in symbol:
            raise PathManagerError(f"无效的股票代码格式: '{symbol}'. 期望格式为 'code.market'")

        parts = symbol.split('.')
        if len(parts) != 2 or not parts[0] or not parts[1]:
            raise PathManagerError(f"无效的股票代码格式: '{symbol}'. 期望格式为 'code.market'")

        code = parts[0]
        market = parts[1].upper()  # 市场代码统一大写

        # 根据规范处理代码大小写
        if self._path_rules['normalize_case'] == 'lower':
            code = code.lower()
        elif self._path_rules['normalize_case'] == 'upper':
            code = code.upper()
        # 'preserve' 保持原样

        result = (code, market)

        # 缓存结果
        with self._parse_cache_lock:
            self._parse_cache[symbol] = result

        return result
    
    def build_partitioned_path(self, symbol: str, period: str, timestamp: Optional[Union[str, datetime]] = None) -> str:
        """
        构建分区存储路径
        
        Args:
            symbol: 股票代码，如 "rb00.SF"
            period: 数据周期，如 "tick", "1m"
            timestamp: 时间戳，支持字符串或datetime对象
            
        Returns:
            str: 分区存储路径
        """
        try:
            # 解析股票代码
            code, market = self.parse_symbol(symbol)
            
            # 处理时间戳
            if timestamp is None:
                dt = datetime.now()
            elif isinstance(timestamp, str):
                if len(timestamp) == 8:  # YYYYMMDD
                    dt = datetime.strptime(timestamp, '%Y%m%d')
                elif len(timestamp) == 14:  # YYYYMMDDHHMMSS
                    dt = datetime.strptime(timestamp, '%Y%m%d%H%M%S')
                else:
                    raise PathManagerError(f"不支持的时间戳格式: {timestamp}")
            elif isinstance(timestamp, datetime):
                dt = timestamp
            else:
                raise PathManagerError(f"不支持的时间戳类型: {type(timestamp)}")
            
            # 选择分区规则
            rule_key = 'tick' if period.lower() == 'tick' else 'default'
            rule = self._partition_rules[rule_key]
            
            # 构建路径
            path_vars = {
                'data_root': self._data_root,
                'market': market,
                'code': code,
                'period': period,
                'year': dt.year,
                'month': dt.month,
                'day': dt.day
            }
            
            result_path = rule['pattern'].format(**path_vars)

            # 标准化路径分隔符
            result_path = os.path.normpath(result_path)

            # 只在需要时输出调试日志（减少性能影响）
            if logger.isEnabledFor(10):  # DEBUG level
                logger.debug(f"构建分区路径: {symbol} {period} -> {result_path}")
            return result_path
            
        except Exception as e:
            logger.error(f"构建分区路径失败: {symbol} {period} - {e}")
            raise PathManagerError(f"路径构建失败: {e}")
    
    def get_base_directory(self, symbol: str, period: str) -> str:
        """
        获取股票数据的基础目录
        
        Args:
            symbol: 股票代码
            period: 数据周期
            
        Returns:
            str: 基础目录路径
        """
        code, market = self.parse_symbol(symbol)
        return os.path.join(self._data_root, market, code, period)
    
    def validate_path(self, path: str) -> bool:
        """
        验证路径是否有效
        
        Args:
            path: 要验证的路径
            
        Returns:
            bool: 路径是否有效
        """
        if not self._path_rules['validate_paths']:
            return True
        
        try:
            # 检查路径格式
            normalized_path = os.path.normpath(path)
            
            # 检查是否在数据根目录下
            if not normalized_path.startswith(self._data_root):
                logger.warning(f"路径不在数据根目录下: {path}")
                return False
            
            # 检查路径组件
            rel_path = os.path.relpath(normalized_path, self._data_root)
            components = rel_path.split(os.sep)
            
            if len(components) < 3:  # 至少需要 market/code/period
                logger.warning(f"路径组件不足: {path}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"路径验证失败: {path} - {e}")
            return False
    
    def normalize_path(self, path: str) -> str:
        """
        标准化路径

        Args:
            path: 原始路径

        Returns:
            str: 标准化后的路径
        """
        return os.path.normpath(os.path.abspath(path))

    def find_partition_files(self, symbol: str, period: str,
                           start_time: Optional[str] = None,
                           end_time: Optional[str] = None) -> List[str]:
        """
        查找分区文件

        Args:
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）

        Returns:
            List[str]: 找到的文件路径列表
        """
        try:
            base_dir = self.get_base_directory(symbol, period)

            if not os.path.exists(base_dir):
                logger.debug(f"基础目录不存在: {base_dir}")
                return []

            files = []

            if period.lower() == 'tick':
                # tick数据按日分区，需要遍历年/月/日目录
                for root, dirs, filenames in os.walk(base_dir):
                    for filename in filenames:
                        if filename.endswith('.parquet'):
                            file_path = os.path.join(root, filename)
                            files.append(file_path)
            else:
                # 其他周期按年分区
                for filename in os.listdir(base_dir):
                    if filename.endswith('.parquet'):
                        file_path = os.path.join(base_dir, filename)
                        files.append(file_path)

            # 按文件路径排序
            files.sort()

            logger.debug(f"找到 {len(files)} 个 {symbol} {period} 文件")
            return files

        except Exception as e:
            logger.error(f"查找分区文件失败: {symbol} {period} - {e}")
            return []

    def get_latest_partition_file(self, symbol: str, period: str) -> Optional[str]:
        """
        获取最新的分区文件

        Args:
            symbol: 股票代码
            period: 数据周期

        Returns:
            Optional[str]: 最新文件路径，如果不存在则返回None
        """
        try:
            base_dir = self.get_base_directory(symbol, period)

            if not os.path.exists(base_dir):
                logger.debug(f"{symbol} {period} 基础目录不存在: {base_dir}")
                return None

            parquet_files = []

            if period.lower() == 'tick':
                # tick数据按日分区，遍历所有子目录
                # 启动批量日志控制
                from utils.time_formatter.date_extraction import start_batch_operation
                start_batch_operation(f"{symbol} {period} 最新文件查找")

                for root, dirs, filenames in os.walk(base_dir):
                    for filename in filenames:
                        if filename.endswith('.parquet'):
                            file_path = os.path.join(root, filename)
                            parquet_files.append(file_path)
            else:
                # 其他周期按年分区
                for filename in os.listdir(base_dir):
                    if filename.endswith('.parquet'):
                        file_path = os.path.join(base_dir, filename)
                        parquet_files.append(file_path)

            if not parquet_files:
                logger.debug(f"{symbol} {period} 目录中无parquet文件")
                return None

            # 使用统一日期提取模块进行排序（替代文件修改时间排序）
            from utils.time_formatter.date_extraction import get_latest_file_by_date, end_batch_operation
            latest_file = get_latest_file_by_date(parquet_files)

            # 结束批量日志控制（仅对tick数据）
            if period.lower() == 'tick':
                end_batch_operation(f"{symbol} {period} 最新文件查找")

            if latest_file:
                logger.debug(f"{symbol} {period} 最新分区文件: {os.path.basename(latest_file)}")
                return latest_file
            else:
                logger.warning(f"{symbol} {period} 无法确定最新分区文件")
                return None

        except Exception as e:
            logger.error(f"获取最新分区文件失败 {symbol} {period}: {e}")
            return None

    def get_earliest_partition_file(self, symbol: str, period: str) -> Optional[str]:
        """
        获取最早的分区文件

        Args:
            symbol: 股票代码
            period: 数据周期

        Returns:
            Optional[str]: 最早文件路径，如果不存在则返回None
        """
        try:
            base_dir = self.get_base_directory(symbol, period)

            if not os.path.exists(base_dir):
                logger.debug(f"{symbol} {period} 基础目录不存在: {base_dir}")
                return None

            parquet_files = []

            if period.lower() == 'tick':
                # tick数据按日分区，遍历所有子目录
                # 启动批量日志控制
                from utils.time_formatter.date_extraction import start_batch_operation
                start_batch_operation(f"{symbol} {period} 最早文件查找")

                for root, dirs, filenames in os.walk(base_dir):
                    for filename in filenames:
                        if filename.endswith('.parquet'):
                            file_path = os.path.join(root, filename)
                            parquet_files.append(file_path)
            else:
                # 其他周期按年分区
                for filename in os.listdir(base_dir):
                    if filename.endswith('.parquet'):
                        file_path = os.path.join(base_dir, filename)
                        parquet_files.append(file_path)

            if not parquet_files:
                logger.debug(f"{symbol} {period} 目录中无parquet文件")
                return None

            # 使用统一日期提取模块进行排序（替代文件修改时间排序）
            from utils.time_formatter.date_extraction import get_earliest_file_by_date, end_batch_operation
            earliest_file = get_earliest_file_by_date(parquet_files)

            # 结束批量日志控制（仅对tick数据）
            if period.lower() == 'tick':
                end_batch_operation(f"{symbol} {period} 最早文件查找")

            if earliest_file:
                logger.debug(f"{symbol} {period} 最早分区文件: {os.path.basename(earliest_file)}")
                return earliest_file
            else:
                logger.warning(f"{symbol} {period} 无法确定最早分区文件")
                return None

        except Exception as e:
            logger.error(f"获取最早分区文件失败 {symbol} {period}: {e}")
            return None

    def path_exists(self, path: str) -> bool:
        """
        检查路径是否存在

        Args:
            path: 要检查的路径

        Returns:
            bool: 路径是否存在
        """
        return os.path.exists(path)

    def ensure_directory(self, path: str) -> bool:
        """
        确保目录存在，如果不存在则创建

        Args:
            path: 目录路径

        Returns:
            bool: 是否成功
        """
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败: {path} - {e}")
            return False

    def clear_cache(self):
        """清空路径缓存"""
        with self._cache_lock:
            self._path_cache.clear()

        with self._parse_cache_lock:
            self._parse_cache.clear()

        logger.info("路径缓存已清空")


# 全局路径管理器实例
_path_manager = None


def get_path_manager() -> PathManager:
    """获取全局路径管理器实例"""
    global _path_manager
    if _path_manager is None:
        _path_manager = PathManager()
    return _path_manager


# 便捷函数
def get_data_root() -> str:
    """获取数据根目录"""
    return get_path_manager().get_data_root()


def parse_symbol(symbol: str) -> Tuple[str, str]:
    """解析股票代码"""
    return get_path_manager().parse_symbol(symbol)


def build_partitioned_path(symbol: str, period: str, timestamp: Optional[Union[str, datetime]] = None) -> str:
    """构建分区存储路径"""
    return get_path_manager().build_partitioned_path(symbol, period, timestamp)


def get_base_directory(symbol: str, period: str) -> str:
    """获取基础目录"""
    return get_path_manager().get_base_directory(symbol, period)


def validate_path(path: str) -> bool:
    """验证路径"""
    return get_path_manager().validate_path(path)


def normalize_path(path: str) -> str:
    """标准化路径"""
    return get_path_manager().normalize_path(path)


def find_partition_files(symbol: str, period: str,
                        start_time: Optional[str] = None,
                        end_time: Optional[str] = None) -> List[str]:
    """查找分区文件"""
    return get_path_manager().find_partition_files(symbol, period, start_time, end_time)


def get_latest_partition_file(symbol: str, period: str) -> Optional[str]:
    """获取最新分区文件"""
    return get_path_manager().get_latest_partition_file(symbol, period)


def get_earliest_partition_file(symbol: str, period: str) -> Optional[str]:
    """获取最早分区文件"""
    return get_path_manager().get_earliest_partition_file(symbol, period)


def path_exists(path: str) -> bool:
    """检查路径是否存在"""
    return get_path_manager().path_exists(path)


def ensure_directory(path: str) -> bool:
    """确保目录存在"""
    return get_path_manager().ensure_directory(path)
