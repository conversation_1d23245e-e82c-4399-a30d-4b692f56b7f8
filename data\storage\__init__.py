#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据存储模块

提供数据存储和读取功能
"""

import os
import sys

# 将项目根目录添加到Python路径
current_file_path = os.path.abspath(__file__)
level_one_up = os.path.dirname(current_file_path)
level_two_up = os.path.dirname(level_one_up)
project_root = os.path.dirname(level_two_up)
sys.path.insert(0, project_root)

# 使用统一日志记录器
from utils.logger import get_unified_logger, LogTarget

# 导入路径管理模块（使用统一路径管理器）
from utils.path_manager import (
    get_data_root,
    build_partitioned_path as get_partitioned_path,
    get_latest_partition_file,
    get_earliest_partition_file,
    parse_symbol
)

# 导入传统路径管理模块（向后兼容）
from data.storage.path_manager import (
    get_partition_files
)

from data.storage.parquet_reader import (
    read_symbol_data,
    read_multiple_symbols,
    read_partitioned_data,
    read_latest_data_timestamp,
    read_first_data_timestamp,
    find_available_data,
    summarize_data_directory
)

from data.storage.parquet_storage import (
    save_to_partition,
    append_to_partition,
    incremental_update,
    get_parquet_metadata
)

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)

logger.debug(LogTarget.FILE, "初始化数据存储模块")
logger.debug(LogTarget.FILE, "已导入路径管理模块")
logger.debug(LogTarget.FILE, "已导入Parquet读取模块")
logger.debug(LogTarget.FILE, "已导入Parquet存储模块")

logger.debug(LogTarget.FILE, "设置模块导出的符号")
# 定义导出的符号
__all__ = [
    # 路径管理模块
    'get_data_root', 'get_partitioned_path', 'get_latest_partition_file',
    'get_earliest_partition_file', 'get_partition_files', 'parse_symbol',

    # 数据读取模块
    'read_symbol_data', 'read_multiple_symbols', 'read_partitioned_data',
    'read_latest_data_timestamp', 'read_first_data_timestamp',
    'find_available_data', 'summarize_data_directory',

    # 数据存储模块
    'save_to_partition', 'append_to_partition', 'incremental_update',
    'get_parquet_metadata'
]

# 记录版本信息
__version__ = '1.0.0'
logger.debug(LogTarget.FILE, f"数据存储模块版本: {__version__}")

logger.info(LogTarget.FILE, "数据存储模块初始化完成，提供数据存储和读取功能")

# 如果未来有从该包导出的内容，可以添加到 __all__
# __all__ = [] 

# 如果未来有从该包导出的内容，可以添加到 __all__
# __all__ = [] 