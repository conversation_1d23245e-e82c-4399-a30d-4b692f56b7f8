#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本地数据处理器模块

专门负责本地数据读取和处理逻辑
"""

import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime

from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget
from data.storage.parquet_reader import read_partitioned_data
from .data_processor import DataProcessor

logger = get_unified_logger(__name__, enhanced=True)


class LocalDataHandler:
    """本地数据处理器"""
    
    def __init__(self, data_root: str = None):
        """
        初始化本地数据处理器
        
        Args:
            data_root: 数据根目录
        """
        self.data_root = data_root or DATA_ROOT
        self.data_processor = DataProcessor()
        
    def get_local_data(self,
                      stock_list: List[str],
                      period: str,
                      start_time: Optional[str] = None,
                      end_time: Optional[str] = None,
                      field_list: Optional[List[str]] = None,
                      set_time_index: bool = True,
                      **kwargs) -> Dict[str, pd.DataFrame]:
        """
        获取本地数据
        
        Args:
            stock_list: 股票代码列表
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            field_list: 字段列表
            set_time_index: 是否设置时间索引
            **kwargs: 其他参数
            
        Returns:
            股票代码到DataFrame的映射
        """
        logger.info(f"读取本地数据: {len(stock_list)}个股票, 周期={period}")
        
        result = {}
        
        for symbol in stock_list:
            try:
                # 验证股票代码格式
                if not self.data_processor.validate_symbol_format(symbol):
                    logger.warning(f"无效的股票代码格式: {symbol}")
                    result[symbol] = pd.DataFrame()
                    continue
                    
                # 读取数据
                df = self._read_single_stock_data(
                    symbol, period, start_time, end_time, field_list, set_time_index
                )
                
                if df is not None and not df.empty:
                    logger.debug(f"成功读取 {symbol} 数据: {len(df)} 行")
                    result[symbol] = df
                else:
                    logger.warning(f"未找到 {symbol} 的本地数据")
                    result[symbol] = pd.DataFrame()
                    
            except Exception as e:
                logger.error(f"读取 {symbol} 本地数据失败: {e}")
                result[symbol] = pd.DataFrame()
                
        logger.info(f"本地数据读取完成: {len([df for df in result.values() if not df.empty])} 个有数据")
        return result
    
    def _read_single_stock_data(self,
                               symbol: str,
                               period: str,
                               start_time: Optional[str],
                               end_time: Optional[str],
                               field_list: Optional[List[str]],
                               set_time_index: bool) -> Optional[pd.DataFrame]:
        """
        读取单个股票的本地数据
        
        Args:
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            field_list: 字段列表
            set_time_index: 是否设置时间索引
            
        Returns:
            DataFrame或None
        """
        try:
            # 使用parquet_reader读取分区数据
            df = read_partitioned_data(
                data_root=self.data_root,
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                columns=field_list,
                set_time_index=set_time_index
            )
            
            return df
            
        except Exception as e:
            logger.error(f"读取 {symbol} 数据时出错: {e}")
            return None
    
    def check_data_exists(self, symbol: str, period: str) -> bool:
        """
        检查本地数据是否存在
        
        Args:
            symbol: 股票代码
            period: 数据周期
            
        Returns:
            是否存在数据
        """
        try:
            # 验证股票代码格式
            if not self.data_processor.validate_symbol_format(symbol):
                return False
                
            # 使用parquet_reader检查数据
            from data.storage.parquet_reader import find_available_data
            
            parts = symbol.split('.')
            code, market = parts[0], parts[1]
            
            data_files = find_available_data(
                data_root=self.data_root,
                market=market,
                code=code,
                period=period
            )
            
            return len(data_files) > 0
            
        except Exception as e:
            logger.error(f"检查 {symbol} 数据存在性时出错: {e}")
            return False
    
    def get_data_summary(self, symbol: str, period: str) -> Optional[Dict]:
        """
        获取数据摘要信息
        
        Args:
            symbol: 股票代码
            period: 数据周期
            
        Returns:
            数据摘要字典或None
        """
        try:
            df = self._read_single_stock_data(symbol, period, None, None, None, False)
            
            if df is None or df.empty:
                return None
                
            # 获取基本信息
            summary = {
                'symbol': symbol,
                'period': period,
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': list(df.columns)
            }
            
            # 获取时间范围
            start_time, end_time = self.data_processor.get_data_time_range(df)
            if start_time and end_time:
                summary['start_time'] = start_time.strftime('%Y-%m-%d %H:%M:%S')
                summary['end_time'] = end_time.strftime('%Y-%m-%d %H:%M:%S')
                summary['time_span_days'] = (end_time - start_time).days
                
            # 获取数据统计
            if 'close' in df.columns:
                close_series = df['close'].dropna()
                if not close_series.empty:
                    summary['price_range'] = {
                        'min': float(close_series.min()),
                        'max': float(close_series.max()),
                        'mean': float(close_series.mean())
                    }
                    
            return summary
            
        except Exception as e:
            logger.error(f"获取 {symbol} 数据摘要时出错: {e}")
            return None