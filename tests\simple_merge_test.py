#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.data_processor.data_merger import merge_dataframes
    import pandas as pd
    
    print("merge_dataframes导入成功")
    
    # 创建测试数据
    df1 = pd.DataFrame({
        'time': [1, 2], 
        'price': [100, 101]
    })
    
    df2 = pd.DataFrame({
        'time': [3, 4], 
        'price': [102, 103]
    })
    
    # 测试合并
    result = merge_dataframes(df1, df2)
    print(f"合并成功，结果行数: {len(result)}")
    print("✅ merge_dataframes修复验证成功")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
