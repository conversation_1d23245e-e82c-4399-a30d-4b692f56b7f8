#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据合并和处理模块

此模块提供合并多个数据源和处理增量更新的功能
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Tuple, Optional

# 导入日志模块
from utils.logger import get_unified_logger

# 导入时间解析模块
from utils.time_formatter.parsing import parse_multi_format_date

# 导入智能时间转换器
from utils.smart_time_converter import smart_to_datetime

# 导入时间转换工具
from utils.time_utils import datetime_to_ms

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


def _convert_time_column_to_ms(df: pd.DataFrame, column_name: str = 'time') -> pd.DataFrame:
    """
    统一的时间列转换函数，将任何时间格式转换为毫秒时间戳

    Args:
        df: 数据框
        column_name: 时间列名称

    Returns:
        pd.DataFrame: 转换后的数据框
    """
    if df.empty or column_name not in df.columns:
        return df

    # 获取第一个非空值来判断类型
    first_value = df[column_name].dropna().iloc[0] if not df[column_name].dropna().empty else None
    if first_value is None:
        return df

    logger.debug(f"转换时间列 '{column_name}'，检测到类型: {type(first_value)}")

    try:
        if isinstance(first_value, pd.Timestamp):
            # Timestamp类型，直接转换为毫秒时间戳
            df[column_name] = df[column_name].apply(lambda x: datetime_to_ms(x) if isinstance(x, pd.Timestamp) else x)
            logger.debug(f"Timestamp类型转换完成")
        elif isinstance(first_value, str):
            # 字符串类型，先转换为datetime再转为时间戳
            df[column_name] = smart_to_datetime(df[column_name]).apply(
                lambda x: datetime_to_ms(x) if not pd.isna(x) else None
            )
            logger.debug(f"字符串类型转换完成")
        elif isinstance(first_value, (int, float)):
            # 数值类型，检查是否需要转换
            if first_value < 1e10:  # 秒时间戳
                df[column_name] = df[column_name] * 1000  # 转换为毫秒
                logger.debug(f"秒时间戳转换为毫秒时间戳")
            else:
                logger.debug(f"已经是毫秒时间戳，无需转换")

        return df
    except Exception as e:
        logger.error(f"时间列转换失败: {e}")
        return df


def get_data_time_range(df: pd.DataFrame) -> Tuple[Optional[datetime], Optional[datetime]]:
    """
    获取数据框的时间范围

    Args:
        df: 数据框，必须包含时间索引或时间列

    Returns:
        Tuple[Optional[datetime], Optional[datetime]]: (最早时间, 最晚时间)，如果无法确定则返回(None, None)
    """
    if df is None or df.empty:
        return None, None

    # 情况1: 使用DatetimeIndex
    if isinstance(df.index, pd.DatetimeIndex):
        return df.index.min(), df.index.max()

    # 情况2: 有'time'列
    if 'time' in df.columns:
        time_col = df['time']

        # 如果time列是datetime类型
        if pd.api.types.is_datetime64_any_dtype(time_col):
            return time_col.min(), time_col.max()

        # 如果time列是数值类型（时间戳）
        elif pd.api.types.is_numeric_dtype(time_col):
            try:
                # 使用智能时间转换器处理时间戳
                if time_col.max() > 1e10:  # 毫秒时间戳
                    datetime_col = smart_to_datetime(time_col, unit='ms')
                else:  # 秒时间戳
                    datetime_col = smart_to_datetime(time_col, unit='s')
                return datetime_col.min(), datetime_col.max()
            except Exception as e:
                logger.warning(f"无法将数值时间戳转换为datetime: {e}")
                return None, None

        # 如果time列是字符串类型
        else:
            try:
                # 尝试转换为datetime
                datetime_col = smart_to_datetime(time_col)
                return datetime_col.min(), datetime_col.max()
            except Exception as e:
                logger.warning(f"无法将字符串时间转换为datetime: {e}")
                return None, None

    # 情况3: 有'date'列
    elif 'date' in df.columns:
        try:
            datetime_col = smart_to_datetime(df['date'])
            return datetime_col.min(), datetime_col.max()
        except Exception as e:
            logger.warning(f"无法将date列转换为datetime: {e}")
            return None, None

    # 无法确定时间列
    logger.warning("数据框中没有找到有效的时间列")
    return None, None


def calculate_incremental_start_time(
    local_end_str: str,
    requested_start: str = "",
    overlap_days: int = 1
) -> Tuple[str, bool]:
    """
    计算增量更新的起始时间

    Args:
        local_end_str: 本地数据的最新时间戳，格式为YYYYMMDD或YYYYMMDDHHMMSS
        requested_start: 用户请求的起始时间，可以为空字符串
        overlap_days: 与现有数据的重叠天数

    Returns:
        Tuple[str, bool]: (计算后的起始时间, 是否需要下载新数据)
    """
    # 如果本地数据的结束时间为空或无效，使用请求的起始时间或空字符串
    if not local_end_str:
        if not requested_start:
            return "", True  # 返回空字符串，让数据源决定最早的数据
        return requested_start, True

    try:
        # 检查本地结束时间是否是异常值（如1970年附近）
        if (local_end_str.startswith('1970') or
            local_end_str.startswith('1969') or
            local_end_str.startswith('196') or
            local_end_str.startswith('175') or  # 添加1750年检查
            local_end_str.startswith('17') or   # 添加17xx年检查
            len(local_end_str) < 8 or
            len(local_end_str) > 14):           # 检查长度异常
            logger.warning(
                f"检测到异常的本地数据时间戳: {local_end_str}，"
                f"可能是系统默认值或时间解析错误"
            )
            # 使用从文件名推断的日期，如果文件名符合特定模式
            has_file_date = (hasattr(calculate_incremental_start_time, 'file_date') and 
                           calculate_incremental_start_time.file_date)
            if has_file_date:
                logger.info(
                    f"使用从文件名推断的日期: "
                    f"{calculate_incremental_start_time.file_date}"
                )
                local_end_str = calculate_incremental_start_time.file_date
                
                # 验证推断日期是否合理 (如果不是以196或197开头)
                if (local_end_str.startswith('1970') or 
                    local_end_str.startswith('1969') or 
                    local_end_str.startswith('196')):
                    logger.warning(
                        f"从文件名推断的日期 {local_end_str} 同样是异常值，"
                        f"将使用空起始时间"
                    )
                    if not requested_start:
                        logger.info("使用空起始时间，让数据源决定最早的可用数据")
                        return "", True  # 返回空字符串，让数据源决定最早的数据
                    return requested_start, True
            else:
                if not requested_start:
                    logger.info("使用空起始时间，让数据源决定最早的可用数据")
                    return "", True  # 返回空字符串，让数据源决定最早的数据
                return requested_start, True

        # 将本地结束时间转换为datetime对象
        dt_obj = None
        if len(local_end_str) == 8:  # YYYYMMDD格式
            dt_obj = datetime.strptime(local_end_str, '%Y%m%d')
        elif len(local_end_str) == 14:  # YYYYMMDDHHMMSS格式
            dt_obj = datetime.strptime(local_end_str, '%Y%m%d%H%M%S')
        else:
            # 尝试自动解析
            dt_obj = parse_multi_format_date(local_end_str)
            if dt_obj is None:
                if not requested_start:
                    logger.info("无法解析时间格式，使用空起始时间，让数据源决定最早的可用数据")
                    return "", True  # 返回空字符串，让数据源决定最早的数据
                return requested_start, True
        
        # 检测极早期日期（如1980年之前）
        early_threshold = datetime(1980, 1, 1)
        if dt_obj < early_threshold:
            logger.warning(
                f"检测到极早期日期: {dt_obj}，可能是系统默认值或时间解析错误"
            )
            if not requested_start:
                logger.info("检测到极早期日期，使用空起始时间，让数据源决定最早的可用数据")
                return "", True  # 返回空字符串，让数据源决定最早的数据
            return requested_start, True
        
        # 考虑重叠天数，往前推overlap_days天
        adjusted_dt = dt_obj - timedelta(days=overlap_days)
        
        # 修改：当回退日期时，重置时分秒为000000（当天开始时间）
        # 这确保了在增量更新时获取完整的日数据，而不是从某个精确时间点开始
        adjusted_dt = adjusted_dt.replace(hour=0, minute=0, second=0)
        
        # 记录调整前后的时间变化
        dt_format = '%Y%m%d%H%M%S'
        original_time_str = dt_obj.strftime(dt_format)
        adjusted_time_str = adjusted_dt.strftime(dt_format)
        adjusted_date_str = adjusted_dt.strftime('%Y%m%d')
        
        logger.info(
            f"增量更新时间调整: 原始时间={original_time_str}, "
            f"回退{overlap_days}天后={adjusted_time_str}"
        )
        logger.info(
            f"实际使用的时间精确到日: {adjusted_date_str} "
            f"(已重置为当天开始时间)"
        )
        
        # 修改：始终使用%Y%m%d格式返回日期，无论原始时间戳是什么格式
        # 这样确保增量更新始终使用精确到日的时间戳，而不是精确到秒
        new_start_time = adjusted_date_str
        
        # 如果新起始时间早于请求的起始时间且请求的起始时间不为空，使用请求的起始时间
        if requested_start and new_start_time < requested_start:
            return requested_start, True
        
        return new_start_time, True
    except Exception as e:
        logger.error(f"计算增量更新起始时间失败: {e}")
        if not requested_start:
            return "", True  # 返回空字符串，让数据源决定最早的数据
        return requested_start, True


def calculate_incremental_start_time_with_trading_calendar(
    local_end_str: str,
    requested_start: str = "",
    overlap_trading_days: int = 1,
    symbol: str = ""
) -> Tuple[str, bool]:
    """
    基于交易日历计算增量更新的起始时间

    Args:
        local_end_str: 本地数据的最新时间戳，格式为YYYYMMDD或YYYYMMDDHHMMSS
        requested_start: 用户请求的起始时间，可以为空字符串
        overlap_trading_days: 与现有数据的重叠交易日数
        symbol: 股票代码，用于识别市场

    Returns:
        Tuple[str, bool]: (计算后的起始时间, 是否需要下载新数据)
    """
    # 如果本地数据的结束时间为空或无效，使用请求的起始时间或空字符串
    if not local_end_str:
        if not requested_start:
            return "", True  # 返回空字符串，让数据源决定最早的数据
        return requested_start, True

    try:
        # 导入交易日历模块
        from utils.calendar.trading_calendar import calculate_overlap_with_fallback

        # 检查本地结束时间是否是异常值（扩展检查范围）
        if (local_end_str.startswith('1970') or
            local_end_str.startswith('1969') or
            local_end_str.startswith('196') or
            local_end_str.startswith('175') or  # 添加1750年检查
            local_end_str.startswith('17') or   # 添加17xx年检查
            len(local_end_str) < 8 or
            len(local_end_str) > 14):           # 检查长度异常
            logger.warning(f"检测到异常的本地数据时间戳: {local_end_str}，使用传统方法计算")
            return calculate_incremental_start_time(local_end_str, requested_start, overlap_trading_days)

        # 提取日期部分（YYYYMMDD）
        end_date = local_end_str[:8] if len(local_end_str) >= 8 else local_end_str

        # 验证日期格式
        try:
            datetime.strptime(end_date, '%Y%m%d')
        except ValueError:
            logger.warning(f"无效的日期格式: {end_date}，使用传统方法计算")
            return calculate_incremental_start_time(local_end_str, requested_start, overlap_trading_days)

        # 检测极早期日期（如1980年之前）
        dt_obj = datetime.strptime(end_date, '%Y%m%d')
        early_threshold = datetime(1980, 1, 1)
        if dt_obj < early_threshold:
            logger.warning(f"检测到极早期日期: {dt_obj}，使用传统方法计算")
            return calculate_incremental_start_time(local_end_str, requested_start, overlap_trading_days)

        # 使用交易日历计算重叠起始时间
        try:
            overlap_start = calculate_overlap_with_fallback(
                end_date=end_date,
                overlap_trading_days=overlap_trading_days,
                symbol=symbol
            )

            logger.info(f"基于交易日历的增量更新时间计算: 原始时间={local_end_str}, "
                       f"重叠{overlap_trading_days}个交易日后={overlap_start}")

            # 如果新起始时间早于请求的起始时间且请求的起始时间不为空，使用请求的起始时间
            if requested_start and overlap_start < requested_start:
                logger.info(f"重叠起始时间({overlap_start})早于请求时间({requested_start})，使用请求时间")
                return requested_start, True

            return overlap_start, True

        except Exception as calendar_error:
            logger.warning(f"交易日历计算失败: {calendar_error}，回退到传统方法")
            return calculate_incremental_start_time(local_end_str, requested_start, overlap_trading_days)

    except Exception as e:
        logger.error(f"基于交易日历的增量更新时间计算失败: {e}，回退到传统方法")
        return calculate_incremental_start_time(local_end_str, requested_start, overlap_trading_days)


def convert_timestamps_to_string(df: pd.DataFrame) -> pd.DataFrame:
    """
    将DataFrame中的所有Timestamp对象转换为字符串，避免PyArrow转换问题
    
    Args:
        df: 需要处理的DataFrame
        
    Returns:
        pd.DataFrame: 处理后的DataFrame
    """
    if df is None or df.empty:
        return df
    
    # 创建一个副本以避免修改原始数据
    result_df = df.copy()
    
    # 处理索引
    if isinstance(result_df.index, pd.DatetimeIndex):
        # 保存索引名称
        index_name = result_df.index.name
        # 转换为字符串格式的索引
        result_df.index = result_df.index.astype(str)
        # 恢复索引名称
        result_df.index.name = index_name
    
    # 处理datetime类型的列
    for col in result_df.select_dtypes(include=['datetime64']).columns:
        logger.debug(f"将datetime列 '{col}' 转换为字符串")
        result_df[col] = result_df[col].astype(str)
    
    # 处理可能包含Timestamp对象的object类型列
    for col in result_df.select_dtypes(include=['object']).columns:
        # 取样本检查是否包含Timestamp对象
        sample = result_df[col].dropna().head(10)
        if any(isinstance(x, pd.Timestamp) for x in sample):
            logger.debug(f"列 '{col}' 包含Timestamp对象，转换为字符串")
            result_df[col] = result_df[col].apply(
                lambda x: str(x) if isinstance(x, pd.Timestamp) else x
            )
    
    return result_df


def merge_dataframes(
    old_df: pd.DataFrame,
    new_df: pd.DataFrame,
    force_update: bool = False
) -> pd.DataFrame:
    """
    合并新旧数据框，确保不会产生重复数据

    Args:
        old_df: 旧数据框
        new_df: 新数据框
        force_update: 是否强制用新数据覆盖重叠部分

    Returns:
        pd.DataFrame: 合并后的数据框
    """
    if old_df is None or old_df.empty:
        return new_df

    if new_df is None or new_df.empty:
        return old_df
    
    # 记录合并前的行数和数据统计
    old_rows = len(old_df)
    new_rows = len(new_df)
    logger.debug(f"数据合并开始 - 旧数据: {old_rows} 行, 新数据: {new_rows} 行")

    # 记录数据框的基本信息
    logger.debug(f"旧数据列: {list(old_df.columns)}")
    logger.debug(f"新数据列: {list(new_df.columns)}")
    logger.debug(f"旧数据索引类型: {type(old_df.index)}")
    logger.debug(f"新数据索引类型: {type(new_df.index)}")
    
    # 保留原始数据格式，但确保time列类型一致
    logger.debug("保留原始数据格式，确保time列类型一致")
    old_df_processed = old_df.copy()
    new_df_processed = new_df.copy()
    
    # 统一转换时间列为毫秒时间戳，简化后续处理逻辑
    if 'time' in old_df_processed.columns and 'time' in new_df_processed.columns:
        logger.debug("开始统一时间列格式转换")
        old_df_processed = _convert_time_column_to_ms(old_df_processed, 'time')
        new_df_processed = _convert_time_column_to_ms(new_df_processed, 'time')
        logger.debug("时间列格式转换完成")

    # 确保两个数据框有相同的列
    common_columns = list(set(old_df_processed.columns).intersection(set(new_df_processed.columns)))
    if not common_columns:
        logger.warning("新旧数据框没有共同的列，无法合并")
        return old_df  # 返回原始旧数据，保持不变

    # 增强型合并逻辑，避免产生重复数据
    # 如果数据框使用DatetimeIndex
    if isinstance(old_df.index, pd.DatetimeIndex) and isinstance(new_df.index, pd.DatetimeIndex):
        logger.debug("检测到DatetimeIndex，使用索引合并")
        
        # 在合并前先检查重复索引
        old_dup = old_df_processed.index.duplicated().sum()
        new_dup = new_df_processed.index.duplicated().sum()
        if old_dup > 0:
            logger.warning(f"旧数据中有{old_dup}个重复索引，将保留第一次出现的")
            old_df_processed = old_df_processed[~old_df_processed.index.duplicated(keep='first')]
        if new_dup > 0:
            logger.warning(f"新数据中有{new_dup}个重复索引，将保留第一次出现的")
            new_df_processed = new_df_processed[~new_df_processed.index.duplicated(keep='first')]
        
        # 检查新旧数据中的重叠部分
        overlap_indices = old_df_processed.index.intersection(new_df_processed.index)
        overlap_count = len(overlap_indices)
        logger.debug(f"新旧数据有{overlap_count}个重叠的索引")
        
        # 根据force_update参数处理重叠部分
        if overlap_count > 0:
            if force_update:
                # 从旧数据中移除重叠部分
                logger.debug("从旧数据中移除重叠部分，使用新数据替代")
                old_df_processed = old_df_processed.drop(overlap_indices)
            else:
                # 从新数据中移除重叠部分
                logger.debug("从新数据中移除重叠部分，保留旧数据")
                new_df_processed = new_df_processed.drop(overlap_indices)
        
        # 合并数据 - 使用统一索引管理器
        from utils.data_processor.index_manager import IndexManager
        merged_df = IndexManager.safe_concat([old_df_processed, new_df_processed])

        if merged_df is None:
            logger.error("统一索引管理器合并失败，使用传统方式")
            merged_df = pd.concat([old_df_processed, new_df_processed], ignore_index=False)

        # 按索引排序
        try:
            merged_df = merged_df.sort_index()
            # 验证合并后的索引格式
            IndexManager.log_index_info(merged_df, "DatetimeIndex数据合并后")
        except Exception as e:
            logger.warning(f"按索引排序失败: {e}")
        
        # 最终验证，确保没有重复索引
        final_dup = merged_df.index.duplicated().sum()
        if final_dup > 0:
            logger.warning(f"合并后数据仍有{final_dup}个重复索引，将保留第一次出现的")
            merged_df = merged_df[~merged_df.index.duplicated(keep='first')]

        # 记录处理后的实际数据行数
        old_processed_rows = len(old_df_processed)
        new_processed_rows = len(new_df_processed)
        merged_rows = len(merged_df)

        # 详细的数据处理统计信息
        logger.debug(f"数据预处理统计:")
        logger.debug(f"  - 旧数据: {old_rows} 行 -> {old_processed_rows} 行 (去重: {old_rows - old_processed_rows})")
        logger.debug(f"  - 新数据: {new_rows} 行 -> {new_processed_rows} 行 (去重: {new_rows - new_processed_rows})")
        logger.debug(f"  - 重叠数据: {overlap_count} 行")

        # 计算预期的合并结果
        if force_update:
            # 强制更新模式：旧数据去除重叠部分 + 新数据全部
            expected_rows = (old_processed_rows - overlap_count) + new_processed_rows
        else:
            # 保留旧数据模式：旧数据全部 + 新数据去除重叠部分
            expected_rows = old_processed_rows + (new_processed_rows - overlap_count)

        logger.debug(f"数据合并完成 - 预期: {expected_rows} 行, 实际: {merged_rows} 行")
        logger.debug(f"数据变化: 净增加 {merged_rows - old_rows} 行")

        # 精确的数据完整性验证
        if merged_rows != expected_rows:
            logger.error(f"数据合并异常: 预期 {expected_rows} 行，实际 {merged_rows} 行，差异 {expected_rows - merged_rows} 行")
            logger.error("这表明数据合并过程中发生了真正的数据丢失或重复")
        else:
            logger.debug("数据合并完整性验证通过")

        return merged_df

    # 如果数据框有'time'列
    elif 'time' in old_df_processed.columns and 'time' in new_df_processed.columns:
        logger.debug("检测到time列，使用time列合并")
        
        # 在合并前先检查重复time值
        old_dup = old_df_processed.duplicated(subset=['time']).sum()
        new_dup = new_df_processed.duplicated(subset=['time']).sum()
        if old_dup > 0:
            logger.warning(f"旧数据中有{old_dup}个重复time值，将保留第一次出现的")
            old_df_processed = old_df_processed.drop_duplicates(subset=['time'], keep='first')
        if new_dup > 0:
            logger.warning(f"新数据中有{new_dup}个重复time值，将保留第一次出现的")
            new_df_processed = new_df_processed.drop_duplicates(subset=['time'], keep='first')
        
        # 检查新旧数据中的重叠部分
        old_times = set(old_df_processed['time'])
        new_times = set(new_df_processed['time'])
        overlap_times = old_times.intersection(new_times)
        overlap_count = len(overlap_times)
        logger.debug(f"新旧数据有{overlap_count}个重叠的time值")
        
        # 根据force_update参数处理重叠部分
        if overlap_count > 0:
            if force_update:
                # 从旧数据中移除重叠部分
                logger.debug("从旧数据中移除重叠部分，使用新数据替代")
                old_df_processed = old_df_processed[~old_df_processed['time'].isin(overlap_times)]
            else:
                # 从新数据中移除重叠部分
                logger.debug("从新数据中移除重叠部分，保留旧数据")
                new_df_processed = new_df_processed[~new_df_processed['time'].isin(overlap_times)]
        
        # 合并数据 - 使用统一索引管理器
        from utils.data_processor.index_manager import IndexManager
        merged_df = IndexManager.safe_concat([old_df_processed, new_df_processed])

        if merged_df is None:
            logger.error("统一索引管理器合并失败，使用传统方式")
            merged_df = pd.concat([old_df_processed, new_df_processed], ignore_index=False)

        # 按时间排序
        try:
            merged_df = merged_df.sort_values('time')
            # 验证合并后的索引格式
            IndexManager.log_index_info(merged_df, "time列数据合并后")
        except Exception as e:
            logger.warning(f"按time列排序失败: {e}")
        
        # 最终验证，确保没有重复time值
        final_dup = merged_df.duplicated(subset=['time']).sum()
        if final_dup > 0:
            logger.warning(f"合并后数据仍有{final_dup}个重复time值，将保留第一次出现的")
            merged_df = merged_df.drop_duplicates(subset=['time'], keep='first')
        
        # 记录处理后的实际数据行数
        old_processed_rows = len(old_df_processed)
        new_processed_rows = len(new_df_processed)
        merged_rows = len(merged_df)

        # 详细的数据处理统计信息
        logger.debug(f"数据预处理统计:")
        logger.debug(f"  - 旧数据: {old_rows} 行 -> {old_processed_rows} 行 (去重: {old_rows - old_processed_rows})")
        logger.debug(f"  - 新数据: {new_rows} 行 -> {new_processed_rows} 行 (去重: {new_rows - new_processed_rows})")
        logger.debug(f"  - 重叠数据: {overlap_count} 行")

        # 计算预期的合并结果
        if force_update:
            # 强制更新模式：旧数据去除重叠部分 + 新数据全部
            expected_rows = (old_processed_rows - overlap_count) + new_processed_rows
        else:
            # 保留旧数据模式：旧数据全部 + 新数据去除重叠部分
            expected_rows = old_processed_rows + (new_processed_rows - overlap_count)

        logger.debug(f"数据合并完成 - 预期: {expected_rows} 行, 实际: {merged_rows} 行")
        logger.debug(f"数据变化: 净增加 {merged_rows - old_rows} 行")

        # 精确的数据完整性验证
        if merged_rows != expected_rows:
            logger.error(f"数据合并异常: 预期 {expected_rows} 行，实际 {merged_rows} 行，差异 {expected_rows - merged_rows} 行")
            logger.error("这表明数据合并过程中发生了真正的数据丢失或重复")
        else:
            logger.debug("数据合并完整性验证通过")

        return merged_df

    # 其他情况 - 数据格式不支持
    else:
        error_msg = f"数据框格式不支持合并: 旧数据索引类型={type(old_df.index)}, 新数据索引类型={type(new_df.index)}"
        logger.error(error_msg)
        raise ValueError(error_msg)


# 注意：根据核心指导思维，删除重复实现，统一使用merge_dataframes函数
# 遵循"一个功能一个实现"原则，不保留向后兼容的重定向函数


def validate_merged_data(df: pd.DataFrame) -> bool:
    """
    验证合并后的数据完整性

    Args:
        df: 合并后的数据框

    Returns:
        bool: 数据是否有效
    """
    if df is None or df.empty:
        logger.warning("数据为空，验证失败")
        return False

    # 检查是否有重复的时间索引
    if isinstance(df.index, pd.DatetimeIndex):
        duplicates = df.index.duplicated()
        if duplicates.any():
            logger.warning(f"数据中有{duplicates.sum()}个重复的时间索引")
            return False

    # 检查是否有重复的time列
    elif 'time' in df.columns:
        duplicates = df.duplicated(subset=['time'])
        if duplicates.any():
            logger.warning(f"数据中有{duplicates.sum()}个重复的time值")
            return False

    # 检查数据是否按时间排序
    if isinstance(df.index, pd.DatetimeIndex):
        if not df.index.is_monotonic_increasing:
            logger.warning("数据未按时间索引排序")
            return False
    elif 'time' in df.columns and pd.api.types.is_datetime64_any_dtype(df['time']):
        if not df['time'].is_monotonic_increasing:
            logger.warning("数据未按time列排序")
            return False

    return True


def merge_dataframes_smart(
    old_data: pd.DataFrame,
    new_data: pd.DataFrame,
    symbol: str = "",
    period: str = "",
    validate_index: bool = True,
    performance_monitoring: bool = True
) -> Optional[pd.DataFrame]:
    """
    智能数据合并函数 - 基于IndexManager标准的完全重构版本
    删除重叠部分的旧数据，与新数据合并，确保数据连续性和完整性

    核心特性：
    1. 严格保持YYYYMMDDHHMMSS索引格式
    2. 使用IndexManager.safe_concat()进行所有合并操作
    3. 删除所有reset_index()和ignore_index=True的使用
    4. 简化排序逻辑，优先使用sort_index()
    5. 完整的索引格式验证和保护机制

    Args:
        old_data: 现有的历史数据
        new_data: 新的增量数据（包含重叠部分）
        symbol: 股票代码，用于日志记录
        period: 数据周期，用于日志记录
        validate_index: 是否验证索引格式（默认True）
        performance_monitoring: 是否启用性能监控（默认True）

    Returns:
        pd.DataFrame: 合并后的数据，保持正确的索引格式

    Raises:
        ValueError: 当输入数据索引格式不正确时
        RuntimeError: 当合并操作失败时
    """
    # 导入IndexManager
    from utils.data_processor.index_manager import IndexManager

    # 性能监控开始
    import time
    start_time = time.time() if performance_monitoring else None

    # 基本数据验证
    if old_data is None or old_data.empty:
        logger.info(f"{symbol} 无历史数据，直接使用新数据")
        result = new_data.copy() if new_data is not None else None
        if result is not None and validate_index:
            IndexManager.log_index_info(result, f"{symbol} 新数据索引")
        return result

    if new_data is None or new_data.empty:
        logger.info(f"{symbol} 无新数据，保持历史数据不变")
        result = old_data.copy()
        if validate_index:
            IndexManager.log_index_info(result, f"{symbol} 历史数据索引")
        return result

    try:
        # 索引格式验证和修复
        if validate_index:
            logger.debug(f"{symbol} 验证输入数据索引格式")

            if not IndexManager.validate_index_format(old_data):
                logger.warning(f"{symbol} 历史数据索引格式不正确，尝试修复")
                old_data = IndexManager.ensure_proper_index(old_data, time_column='time')
                if not IndexManager.validate_index_format(old_data):
                    raise ValueError(f"{symbol} 历史数据索引格式修复失败")

            if not IndexManager.validate_index_format(new_data):
                logger.warning(f"{symbol} 新数据索引格式不正确，尝试修复")
                new_data = IndexManager.ensure_proper_index(new_data, time_column='time')
                if not IndexManager.validate_index_format(new_data):
                    raise ValueError(f"{symbol} 新数据索引格式修复失败")

        # 获取数据时间范围
        new_start, new_end = get_data_time_range(new_data)
        old_start, old_end = get_data_time_range(old_data)

        if new_start is None or new_end is None:
            logger.error(f"{symbol} 无法获取新数据时间范围，使用基础合并")
            merged_data = IndexManager.safe_concat([old_data, new_data])
            if merged_data is None:
                raise RuntimeError(f"{symbol} 基础合并失败")
            return merged_data

        if old_start is None or old_end is None:
            logger.error(f"{symbol} 无法获取历史数据时间范围，直接使用新数据")
            return new_data.copy()

        logger.debug(f"{symbol} 智能合并: 历史数据({old_start.strftime('%Y%m%d %H:%M:%S')}-{old_end.strftime('%Y%m%d %H:%M:%S')}) + 新数据({new_start.strftime('%Y%m%d %H:%M:%S')}-{new_end.strftime('%Y%m%d %H:%M:%S')})")

        # 重叠检测和处理（使用IndexManager统一合并）
        if new_start > old_end:
            # 无重叠，直接连接
            logger.info(f"{symbol} 数据无重叠，直接连接")
            merged_data = IndexManager.safe_concat([old_data, new_data])
        elif new_end <= old_start:
            # 新数据完全在历史数据之前，直接连接
            logger.info(f"{symbol} 新数据在历史数据之前，直接连接")
            merged_data = IndexManager.safe_concat([new_data, old_data])
        else:
            # 有重叠，需要智能处理
            logger.info(f"{symbol} 数据有重叠，执行智能合并")

            try:
                # 智能重叠处理：删除历史数据中重叠的部分
                old_clean = None

                # 优先使用时间列进行过滤
                time_col = None
                for col in ['time', 'timestamp', 'datetime']:
                    if col in old_data.columns:
                        time_col = col
                        break

                if time_col:
                    # 使用时间列进行过滤
                    logger.debug(f"{symbol} 使用时间列 '{time_col}' 进行重叠过滤")

                    if old_data[time_col].dtype == 'int64':
                        # 时间戳格式
                        import time
                        new_start_timestamp = int(time.mktime(new_start.timetuple()) * 1000)  # 毫秒时间戳
                        old_clean = old_data[old_data[time_col] < new_start_timestamp]
                    else:
                        # 字符串格式，直接字符串比较
                        new_start_str = new_start.strftime('%Y%m%d%H%M%S')
                        old_clean = old_data[old_data[time_col] < new_start_str]

                    logger.info(f"{symbol} 基于时间列删除重叠数据: 历史数据从{len(old_data)}行减少到{len(old_clean)}行")

                else:
                    # 使用索引进行过滤
                    logger.debug(f"{symbol} 使用索引进行重叠过滤")

                    try:
                        new_start_str = new_start.strftime('%Y%m%d%H%M%S')

                        # 根据索引类型选择过滤方式
                        if isinstance(old_data.index[0], str):
                            # 字符串索引，直接比较
                            old_clean = old_data[old_data.index < new_start_str]
                        else:
                            # 其他类型索引，转换为时间戳比较
                            import time
                            new_start_timestamp = int(time.mktime(new_start.timetuple()) * 1000)
                            old_clean = old_data[old_data.index < new_start_timestamp]

                        logger.info(f"{symbol} 基于索引删除重叠数据: 历史数据从{len(old_data)}行减少到{len(old_clean)}行")

                    except Exception as index_error:
                        logger.warning(f"{symbol} 索引过滤失败: {index_error}，使用基础合并")
                        old_clean = old_data  # 如果过滤失败，使用全部历史数据

                # 使用IndexManager进行安全合并
                merged_data = IndexManager.safe_concat([old_clean, new_data])

                if merged_data is None:
                    logger.error(f"{symbol} IndexManager合并失败，使用基础合并")
                    merged_data = IndexManager.safe_concat([old_data, new_data])  # 回退到全量合并
                    if merged_data is None:
                        raise RuntimeError(f"{symbol} 所有合并方式都失败")

            except Exception as overlap_error:
                logger.warning(f"{symbol} 重叠处理失败: {overlap_error}，使用基础合并")
                merged_data = IndexManager.safe_concat([old_data, new_data])
                if merged_data is None:
                    raise RuntimeError(f"{symbol} 基础合并失败: {overlap_error}")

        # 统一排序处理（删除错误的冲突检测逻辑）
        try:
            # 优先使用索引排序，保持索引格式一致性
            if merged_data is not None:
                merged_data = merged_data.sort_index()
                logger.debug(f"{symbol} 使用索引排序完成")

                # 验证排序后的索引格式
                if validate_index and not IndexManager.validate_index_format(merged_data):
                    logger.error(f"{symbol} 排序后索引格式验证失败")
                    raise RuntimeError(f"{symbol} 排序破坏了索引格式")

        except Exception as sort_error:
            logger.warning(f"{symbol} 索引排序失败: {sort_error}")
            # 如果索引排序失败，尝试其他排序方式
            try:
                if 'time' in merged_data.columns:
                    merged_data = merged_data.sort_values('time')
                    logger.debug(f"{symbol} 使用time列排序完成")
                else:
                    logger.warning(f"{symbol} 无法排序，保持原始顺序")
            except Exception as fallback_error:
                logger.warning(f"{symbol} 所有排序方式都失败: {fallback_error}")

        # 最终验证
        if merged_data is None:
            raise RuntimeError(f"{symbol} 合并结果为空")

        if not validate_merged_data(merged_data):
            logger.error(f"{symbol} 合并数据验证失败")
            raise RuntimeError(f"{symbol} 合并数据验证失败")

        # 最终索引格式验证
        if validate_index and not IndexManager.validate_index_format(merged_data):
            logger.error(f"{symbol} 最终索引格式验证失败")
            raise RuntimeError(f"{symbol} 最终索引格式验证失败")

        # 记录合并结果
        merged_start, merged_end = get_data_time_range(merged_data)
        if merged_start and merged_end:
            logger.info(f"{symbol} 智能合并完成: {len(old_data)}行历史 + {len(new_data)}行新增 = {len(merged_data)}行合并 "
                       f"(时间范围: {merged_start.strftime('%Y%m%d %H:%M:%S')}-{merged_end.strftime('%Y%m%d %H:%M:%S')})")
        else:
            logger.info(f"{symbol} 智能合并完成: {len(old_data)}行历史 + {len(new_data)}行新增 = {len(merged_data)}行合并")

        # 性能监控结束
        if performance_monitoring and start_time:
            end_time = time.time()
            duration = end_time - start_time
            logger.debug(f"{symbol} 智能合并耗时: {duration:.6f}秒")

        # 记录最终索引信息
        IndexManager.log_index_info(merged_data, f"{symbol} 智能合并完成")

        return merged_data

    except Exception as e:
        logger.error(f"{symbol} 智能数据合并失败: {e}", exc_info=True)
        # 不再回退到可能有问题的旧方法，直接抛出异常
        raise RuntimeError(f"{symbol} 智能合并失败: {e}") from e


def filter_data_by_time_range(
    df: pd.DataFrame,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> pd.DataFrame:
    """
    根据时间范围过滤DataFrame

    Args:
        df: 待过滤的DataFrame
        start_time: 开始时间，格式为YYYYMMDD或YYYYMMDD HHMMSS
        end_time: 结束时间，格式为YYYYMMDD或YYYYMMDD HHMMSS

    Returns:
        pd.DataFrame: 过滤后的DataFrame
    """
    if df is None or df.empty:
        return pd.DataFrame()

    # 寻找时间列
    time_col = None
    for col in ['datetime', 'time', 'timestamp']:
        if col in df.columns:
            time_col = col
            break

    if time_col is None:
        logger.warning("数据中没有找到时间列，无法按时间过滤")
        return df

    # 确保时间列是datetime类型 - 使用智能时间转换器
    if not pd.api.types.is_datetime64_any_dtype(df[time_col]):
        try:
            # 使用智能时间转换器自动检测格式
            if pd.api.types.is_numeric_dtype(df[time_col]):
                if df[time_col].iloc[0] > 1e12:  # 毫秒时间戳
                    df[time_col] = smart_to_datetime(df[time_col], unit='ms')
                else:  # 秒时间戳
                    df[time_col] = smart_to_datetime(df[time_col], unit='s')
            else:
                # 字符串格式
                df[time_col] = smart_to_datetime(df[time_col])
        except Exception as e:
            logger.error(f"转换时间列失败: {e}")
            return df

    # 复制一份数据，避免修改原始数据
    filtered_df = df.copy()

    # 根据start_time过滤
    if start_time:
        try:
            start_dt = None
            # 处理不同格式的start_time
            if len(start_time) == 8:  # YYYYMMDD
                start_dt = smart_to_datetime(start_time, format='%Y%m%d')
            elif len(start_time) == 15:  # YYYYMMDD HHMMSS
                start_dt = smart_to_datetime(f"{start_time[:8]} {start_time[9:]}",
                                          format='%Y%m%d %H%M%S')
            else:
                # 尝试自动解析
                start_dt = smart_to_datetime(start_time)

            if start_dt is not None:
                filtered_df = filtered_df[filtered_df[time_col] >= start_dt]
        except Exception as e:
            logger.warning(f"解析开始时间 '{start_time}' 失败: {e}")

    # 根据end_time过滤
    if end_time:
        try:
            end_dt = None
            # 处理不同格式的end_time
            if len(end_time) == 8:  # YYYYMMDD
                # 添加一天减1秒，确保包含当天所有时间
                from datetime import timedelta
                end_dt = smart_to_datetime(end_time, format='%Y%m%d') + \
                    timedelta(days=1) - timedelta(seconds=1)
            elif len(end_time) == 15:  # YYYYMMDD HHMMSS
                end_dt = smart_to_datetime(f"{end_time[:8]} {end_time[9:]}",
                                        format='%Y%m%d %H%M%S')
            else:
                # 尝试自动解析
                end_dt = smart_to_datetime(end_time)

            if end_dt is not None:
                filtered_df = filtered_df[filtered_df[time_col] <= end_dt]
        except Exception as e:
            logger.warning(f"解析结束时间 '{end_time}' 失败: {e}")

    return filtered_df
