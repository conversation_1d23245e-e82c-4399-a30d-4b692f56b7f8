#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极简时间转换工具模块

基于用户建议，采用最简单高效的时间转换方法：
- 单个时间戳：datetime.fromtimestamp(timestamp_ms / 1000)
- 批量时间戳：[datetime.fromtimestamp(ts/1000) for ts in timestamps]
- 高性能批量：smart_to_datetime(timestamps, unit='ms', utc=True).tz_convert('Asia/Shanghai').tz_localize(None)

设计理念：
1. 简单优于复杂
2. 性能优于功能
3. 直接优于抽象
4. 用户建议优于过度工程化

禁用规则：
- 严禁使用 smart_to_datetime() 裸调用（会导致8小时时区偏移）
- 严禁添加不必要的日志、错误处理、类型检查
- 严禁过度抽象和复杂化
"""

import datetime
import pandas as pd
import numpy as np
import time
from typing import Union, List


class TimeConversionError(Exception):
    """时间转换异常类 - 极简实现"""
    pass


def simple_ms_to_datetime(timestamp_ms: Union[int, float]) -> datetime.datetime:
    """
    单个毫秒时间戳转datetime - 用户的高效方法
    
    Args:
        timestamp_ms: 毫秒时间戳
        
    Returns:
        datetime.datetime: 本地时区的datetime对象
        
    Example:
        >>> dt = simple_ms_to_datetime(1737158400000)
        >>> print(dt)  # 2025-01-18 08:00:00
    """
    return datetime.datetime.fromtimestamp(timestamp_ms / 1000)


def simple_ms_to_datetime_list(timestamps: Union[List, np.ndarray, pd.Series]) -> pd.DatetimeIndex:
    """
    批量毫秒时间戳转DatetimeIndex - 用户的简化方法
    
    Args:
        timestamps: 毫秒时间戳数组
        
    Returns:
        pd.DatetimeIndex: 本地时区的DatetimeIndex
        
    Example:
        >>> timestamps = [1737158400000, 1737158460000, 1737158520000]
        >>> dt_index = simple_ms_to_datetime_list(timestamps)
        >>> print(dt_index[0])  # 2025-01-18 08:00:00
    """
    return pd.DatetimeIndex([datetime.datetime.fromtimestamp(ts / 1000) for ts in timestamps])


def fast_ms_to_datetime_index(timestamps: Union[List, np.ndarray, pd.Series]) -> pd.DatetimeIndex:
    """
    高性能批量毫秒时间戳转DatetimeIndex - 使用datetime.fromtimestamp避免时区问题

    Args:
        timestamps: 毫秒时间戳数组

    Returns:
        pd.DatetimeIndex: 本地时区的DatetimeIndex

    Note:
        直接使用datetime.fromtimestamp，避免pd.to_datetime的时区问题

    Example:
        >>> timestamps = [1737158400000, 1737158460000, 1737158520000]
        >>> dt_index = fast_ms_to_datetime_index(timestamps)
        >>> print(dt_index[0])  # 2025-01-18 08:00:00
    """
    # 转换为numpy数组以便处理
    if isinstance(timestamps, pd.Series):
        timestamps = timestamps.values
    elif isinstance(timestamps, list):
        timestamps = np.array(timestamps)

    # 优化：使用向量化操作处理None值，避免Python循环
    if isinstance(timestamps, (list, tuple)):
        timestamps = np.array(timestamps, dtype=float)
    elif isinstance(timestamps, pd.Series):
        timestamps = timestamps.values

    # 使用numpy的向量化操作
    valid_mask = ~pd.isna(timestamps)
    datetime_list = np.empty(len(timestamps), dtype=object)

    # 只对有效值进行转换
    if valid_mask.any():
        valid_timestamps = timestamps[valid_mask]
        valid_datetimes = [datetime.datetime.fromtimestamp(ts / 1000) for ts in valid_timestamps]
        datetime_list[valid_mask] = valid_datetimes

    # 无效值设为NaT
    datetime_list[~valid_mask] = pd.NaT
    return pd.DatetimeIndex(datetime_list)


def simple_string_to_datetime_list(time_strings: Union[List, pd.Index], 
                                  format_str: str = '%Y%m%d%H%M%S') -> pd.DatetimeIndex:
    """
    字符串时间转DatetimeIndex - 避免pd.to_datetime的时区问题
    
    Args:
        time_strings: 时间字符串数组
        format_str: 时间格式，默认'%Y%m%d%H%M%S'
        
    Returns:
        pd.DatetimeIndex: 本地时区的DatetimeIndex
        
    Example:
        >>> time_strings = ['20250118080000', '20250118080100']
        >>> dt_index = simple_string_to_datetime_list(time_strings)
        >>> print(dt_index[0])  # 2025-01-18 08:00:00
    """
    return pd.DatetimeIndex([datetime.datetime.strptime(str(ts), format_str) for ts in time_strings])


# 向后兼容的别名函数（保持接口一致性）
def ms_to_datetime(timestamp_ms: Union[int, float]) -> datetime.datetime:
    """向后兼容别名：单个毫秒时间戳转换"""
    return simple_ms_to_datetime(timestamp_ms)


def ms_to_datetime_index(timestamps: Union[List, np.ndarray, pd.Series]) -> pd.DatetimeIndex:
    """向后兼容别名：批量毫秒时间戳转换（使用最快方法）"""
    return fast_ms_to_datetime_index(timestamps)


def s_to_datetime(timestamp_s: Union[int, float]) -> datetime.datetime:
    """
    单个秒时间戳转datetime
    
    Args:
        timestamp_s: 秒时间戳
        
    Returns:
        datetime.datetime: 本地时区的datetime对象
    """
    return datetime.datetime.fromtimestamp(timestamp_s)


def s_to_datetime_index(timestamps: Union[List, np.ndarray, pd.Series]) -> pd.DatetimeIndex:
    """
    批量秒时间戳转DatetimeIndex - 使用datetime.fromtimestamp避免时区问题

    Args:
        timestamps: 秒时间戳数组

    Returns:
        pd.DatetimeIndex: 本地时区的DatetimeIndex
    """
    # 转换为numpy数组以便处理
    if isinstance(timestamps, pd.Series):
        timestamps = timestamps.values
    elif isinstance(timestamps, list):
        timestamps = np.array(timestamps)

    # 优化：使用向量化操作处理None值，避免Python循环
    if isinstance(timestamps, (list, tuple)):
        timestamps = np.array(timestamps, dtype=float)
    elif isinstance(timestamps, pd.Series):
        timestamps = timestamps.values

    # 使用numpy的向量化操作
    valid_mask = ~pd.isna(timestamps)
    datetime_list = np.empty(len(timestamps), dtype=object)

    # 只对有效值进行转换
    if valid_mask.any():
        valid_timestamps = timestamps[valid_mask]
        valid_datetimes = [datetime.datetime.fromtimestamp(ts) for ts in valid_timestamps]
        datetime_list[valid_mask] = valid_datetimes

    # 无效值设为NaT
    datetime_list[~valid_mask] = pd.NaT
    return pd.DatetimeIndex(datetime_list)


def datetime_to_ms(dt: datetime.datetime) -> int:
    """
    datetime转毫秒时间戳 - 极简实现

    Args:
        dt: datetime对象

    Returns:
        int: 毫秒时间戳

    Example:
        >>> dt = datetime.datetime(2025, 1, 18, 8, 0, 0)
        >>> ms = datetime_to_ms(dt)
        >>> print(ms)  # 1737158400000
    """
    # 使用time.mktime()确保与fromtimestamp()的一致性
    timestamp_sec = time.mktime(dt.timetuple()) + dt.microsecond / 1000000.0
    return int(timestamp_sec * 1000)


def datetime_to_s(dt: datetime.datetime) -> int:
    """
    datetime转秒时间戳 - 极简实现

    Args:
        dt: datetime对象

    Returns:
        int: 秒时间戳

    Example:
        >>> dt = datetime.datetime(2025, 1, 18, 8, 0, 0)
        >>> s = datetime_to_s(dt)
        >>> print(s)  # 1737158400
    """
    # 使用time.mktime()确保与fromtimestamp()的一致性
    return int(time.mktime(dt.timetuple()))


def format_datetime(dt: Union[datetime.datetime, pd.DatetimeIndex],
                   format_str: str = '%Y%m%d%H%M%S') -> Union[str, pd.Index]:
    """
    时间格式化 - 极简实现

    Args:
        dt: datetime对象或DatetimeIndex
        format_str: 格式化字符串，默认为'%Y%m%d%H%M%S'

    Returns:
        格式化后的字符串或字符串索引

    Example:
        >>> dt = datetime.datetime(2025, 1, 18, 8, 0, 0)
        >>> formatted = format_datetime(dt)
        >>> print(formatted)  # '20250118080000'
    """
    if isinstance(dt, pd.DatetimeIndex):
        return dt.strftime(format_str)
    else:
        return dt.strftime(format_str)


def verify_conversion(original_timestamp: int, converted_datetime: datetime.datetime,
                     unit: str = 'ms') -> bool:
    """
    时间转换验证 - 极简实现

    Args:
        original_timestamp: 原始时间戳
        converted_datetime: 转换后的datetime对象
        unit: 时间戳单位，'ms'或's'

    Returns:
        bool: 转换是否正确

    Example:
        >>> timestamp = 1737158400000
        >>> dt = simple_ms_to_datetime(timestamp)
        >>> is_correct = verify_conversion(timestamp, dt, 'ms')
        >>> print(is_correct)  # True
    """
    try:
        # 将转换后的datetime重新转换为时间戳
        if unit == 'ms':
            reconverted_timestamp = datetime_to_ms(converted_datetime)
        else:
            reconverted_timestamp = datetime_to_s(converted_datetime)

        # 比较原始时间戳和重新转换的时间戳，允许1单位的误差
        return abs(original_timestamp - reconverted_timestamp) <= 1

    except Exception:
        return False


# 向后兼容的别名函数（用于替代旧的unified_converter）
def _convert_ms_timestamp_to_datetime(timestamp: Union[int, float]) -> datetime.datetime:
    """向后兼容别名：替代旧的unified_converter函数"""
    return simple_ms_to_datetime(timestamp)


def convert_ms_timestamp_to_datetimeindex(timestamps: Union[List, np.ndarray, pd.Series]) -> pd.DatetimeIndex:
    """向后兼容别名：替代旧的unified_converter函数"""
    return fast_ms_to_datetime_index(timestamps)


def convert_s_timestamp_to_datetimeindex(timestamps: Union[List, np.ndarray, pd.Series]) -> pd.DatetimeIndex:
    """向后兼容别名：替代旧的unified_converter函数"""
    return s_to_datetime_index(timestamps)


# 禁用函数检查
def check_forbidden_usage():
    """
    检查代码中是否使用了禁用的时间转换方法
    
    这个函数可以在开发时调用，检查是否有人使用了smart_to_datetime()等禁用方法
    """
    import inspect
    import warnings
    
    # 获取调用栈
    frame = inspect.currentframe()
    try:
        # 检查调用栈中是否有pd.to_datetime的使用
        while frame:
            code = frame.f_code
            if 'pd.to_datetime' in str(code.co_names):
                warnings.warn(
                    f"检测到禁用的smart_to_datetime()调用在文件 {code.co_filename}:{frame.f_lineno}\n"
                    f"请使用 utils.time_utils 中的简化方法替代",
                    UserWarning,
                    stacklevel=2
                )
            frame = frame.f_back
    finally:
        del frame


# 性能基准测试函数
def benchmark_time_conversion_methods(timestamps_count: int = 1000):
    """
    时间转换方法性能基准测试
    
    Args:
        timestamps_count: 测试的时间戳数量
        
    Returns:
        dict: 各种方法的性能对比结果
    """
    import time
    from utils.smart_time_converter import smart_to_datetime

    # 生成测试数据
    base_timestamp = 1737158400000  # 2025-01-18 08:00:00
    timestamps = [base_timestamp + i * 60000 for i in range(timestamps_count)]
    
    results = {}
    
    # 测试用户简化方法
    start_time = time.time()
    result1 = simple_ms_to_datetime_list(timestamps)
    results['用户简化方法'] = time.time() - start_time
    
    # 测试高性能方法
    start_time = time.time()
    result2 = fast_ms_to_datetime_index(timestamps)
    results['高性能方法'] = time.time() - start_time
    
    # 验证结果一致性
    results['结果一致'] = result1.equals(result2)
    
    return results


if __name__ == '__main__':
    # 简单测试
    print("=== 极简时间转换工具测试 ===")
    
    # 测试单个转换
    timestamp = 1737158400000
    dt = simple_ms_to_datetime(timestamp)
    print(f"单个转换: {timestamp} -> {dt}")
    
    # 测试批量转换
    timestamps = [1737158400000, 1737158460000, 1737158520000]
    dt_list = simple_ms_to_datetime_list(timestamps)
    print(f"批量转换: {len(timestamps)} 个时间戳 -> {dt_list[0]} 到 {dt_list[-1]}")
    
    # 测试高性能转换
    dt_fast = fast_ms_to_datetime_index(timestamps)
    print(f"高性能转换: {len(timestamps)} 个时间戳 -> {dt_fast[0]} 到 {dt_fast[-1]}")
    
    # 性能基准测试
    print("\n=== 性能基准测试 ===")
    benchmark_results = benchmark_time_conversion_methods(1000)
    for method, duration in benchmark_results.items():
        if method != '结果一致':
            print(f"{method}: {duration:.4f}秒")
    print(f"结果一致性: {benchmark_results['结果一致']}")
    
    print("\n✅ 极简时间转换工具测试完成")
