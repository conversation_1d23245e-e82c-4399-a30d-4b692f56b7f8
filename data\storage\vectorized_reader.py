#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量化数据读取模块

基于Pandas实现的高性能向量化数据读取框架，支持：
- 向量化多文件读取（pandas.concat实现）
- 多线程并行处理，结果按时间顺序排列
- 智能数据排序，确保输出时间有序
- 智能缓存和内存优化
- 完全保持数据原始结构

性能特点：
- 使用pandas原生向量化操作，完全保持数据原始性
- 支持多线程并行文件读取，自动按文件顺序重排结果
- 内存使用优化，支持大数据集处理
- 异步处理，减少I/O等待时间
- 自动数据排序验证，确保时间索引单调递增

数据顺序保证：
- 多线程读取后按输入文件顺序重新排列
- 数据合并后自动检查并排序时间索引
- 日志预览始终显示时间有序的数据

作者: AI Assistant
创建时间: 2025-07-15
更新时间: 2025-07-16
版本: 2.1.0 - 优化数据顺序处理
"""

import os
import asyncio
import time
from typing import List, Optional, Dict, Union, Any, Tuple
from pathlib import Path
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import functools
import hashlib

# 导入现有模块
from data.storage.path_manager import get_partition_files
from utils.logger import get_unified_logger

logger = get_unified_logger(__name__)


def performance_monitor(func):
    """
    性能监控装饰器
    
    监控函数执行时间和数据处理量
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 记录执行时间
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 记录性能日志
        if result is not None and hasattr(result, '__len__'):
            data_size = len(result) if hasattr(result, '__len__') else 0
            logger.debug(f"向量化数据读取: {func.__name__} 处理 {data_size} 条数据，耗时 {execution_time:.6f} 秒")
        else:
            logger.debug(f"向量化数据读取: {func.__name__} 耗时 {execution_time:.6f} 秒")
        
        return result
    return wrapper


class VectorizedDataReader:
    """
    向量化数据读取器

    提供高性能的向量化数据读取功能，支持多种优化策略。

    特性：
    - 多线程并行读取，结果按文件时间顺序排列
    - 智能数据排序，确保输出数据按时间索引有序
    - 完全保持原始数据结构，无任何数据修改
    - 支持缓存机制，提高重复查询性能
    - 自动数据完整性验证和错误处理

    数据顺序保证：
    - 多线程读取完成后，结果按输入文件顺序重新排列
    - pandas.concat后自动检查并排序，确保时间索引单调递增
    - 日志预览显示的数据始终按时间顺序排列
    """
    
    def __init__(self, enable_cache: bool = True, cache_size: int = 100):
        """
        初始化向量化数据读取器
        
        Args:
            enable_cache: 是否启用缓存
            cache_size: 缓存大小限制
        """
        self.enable_cache = enable_cache
        self.cache_size = cache_size
        self._cache = {} if enable_cache else None
        self._performance_stats = {
            'total_calls': 0,
            'total_time': 0.0,
            'cache_hits': 0,
            'total_files_read': 0,
            'total_data_size': 0
        }
        
        # 初始化完成
        logger.info("Pandas向量化读取器初始化成功")
    
    def _generate_cache_key(self, files: List[str], columns: Optional[List[str]] = None) -> str:
        """
        生成缓存键
        
        Args:
            files: 文件路径列表
            columns: 列名列表
            
        Returns:
            str: 缓存键
        """
        # 使用文件路径和修改时间生成缓存键
        file_info = []
        for file_path in files:
            if os.path.exists(file_path):
                mtime = os.path.getmtime(file_path)
                file_info.append(f"{file_path}:{mtime}")
        
        key_data = "|".join(file_info)
        if columns:
            key_data += f"|cols:{','.join(columns)}"
        
        return hashlib.md5(key_data.encode()).hexdigest()
    
    @performance_monitor
    def read_files_vectorized(self, files: List[str],
                            columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        使用Pandas向量化读取多个parquet文件

        Args:
            files: 文件路径列表
            columns: 要读取的列名列表

        Returns:
            pd.DataFrame: 合并后的数据（完全保持原始结构）

        Raises:
            FileNotFoundError: 当文件不存在时
            ValueError: 当文件列表为空时
        """

        # 输入验证
        if not files:
            raise ValueError("文件列表不能为空")

        # 检查缓存
        cache_key = self._generate_cache_key(files, columns) if self.enable_cache else None
        if cache_key and cache_key in self._cache:
            logger.debug(f"缓存命中: {cache_key}")
            self._performance_stats['cache_hits'] += 1
            return self._cache[cache_key].copy()

        # 验证文件存在性
        missing_files = [f for f in files if not os.path.exists(f)]
        if missing_files:
            raise FileNotFoundError(f"以下文件不存在: {missing_files}")

        try:
            logger.debug(f"使用Pandas向量化读取 {len(files)} 个文件")

            # 执行向量化读取
            start_time = time.time()

            # 根据文件数量决定是否使用多线程
            if len(files) >= 3:
                # 多线程并行读取
                dfs = self._read_files_parallel(files, columns)
            else:
                # 单线程读取（小文件数量时避免线程开销）
                dfs = []
                for file_path in files:
                    df = pd.read_parquet(file_path, columns=columns)
                    if df is not None and not df.empty:
                        dfs.append(df)
                    else:
                        logger.warning(f"文件为空或读取失败: {file_path}")

            if not dfs:
                logger.warning("没有成功读取任何文件")
                return None

            # 使用统一的索引管理器进行安全合并
            from utils.data_processor.index_manager import IndexManager

            result_df = IndexManager.safe_concat(dfs, axis=0)

            if result_df is None:
                logger.error("统一索引管理器合并失败，使用传统方式")
                result_df = pd.concat(dfs, axis=0, ignore_index=False)

            # 确保数据按索引（时间）排序，提供一致的数据顺序
            if result_df is not None and not result_df.empty:
                # 验证索引格式
                IndexManager.log_index_info(result_df, "向量化读取合并后")

                if not result_df.index.is_monotonic_increasing:
                    logger.debug("数据索引未排序，正在按索引排序...")
                    result_df = result_df.sort_index()
                    logger.debug("数据索引排序完成")
                else:
                    logger.debug("数据索引已按时间排序")

            query_time = time.time() - start_time

            if result_df is not None and not result_df.empty:
                logger.debug(f"Pandas向量化读取源数据成功: {len(result_df)} 行数据，耗时 {query_time:.6f} 秒，数据预览: \n{result_df}")

                # 更新统计信息
                self._performance_stats['total_files_read'] += len(files)
                self._performance_stats['total_data_size'] += len(result_df)

                # 缓存结果
                if self.enable_cache and cache_key:
                    if len(self._cache) >= self.cache_size:
                        # 简单的LRU：删除第一个缓存项
                        oldest_key = next(iter(self._cache))
                        del self._cache[oldest_key]
                    self._cache[cache_key] = result_df.copy()

                return result_df
            else:
                logger.warning("Pandas向量化读取返回空结果")
                return None

        except Exception as e:
            # 提供详细的错误信息用于调试
            error_msg = f"Pandas向量化读取失败: {e}"
            logger.error(error_msg)
            logger.error(f"文件列表: {files}")

            # 直接抛出异常
            raise RuntimeError(f"向量化数据读取失败: {e}") from e

    def _read_files_parallel(self, files: List[str], columns: Optional[List[str]] = None,
                           max_workers: int = 4) -> List[pd.DataFrame]:
        """
        多线程并行读取文件，保证结果按文件路径顺序排列

        Args:
            files: 文件路径列表（应按时间顺序排列）
            columns: 要读取的列名列表
            max_workers: 最大线程数

        Returns:
            List[pd.DataFrame]: 按输入文件顺序排列的DataFrame列表
        """
        def read_single_file(file_path: str) -> Optional[pd.DataFrame]:
            """读取单个文件"""
            try:
                df = pd.read_parquet(file_path, columns=columns)
                if df is not None and not df.empty:
                    logger.debug(f"并行读取文件成功: {file_path}, 行数: {len(df)}")
                    return df
                else:
                    logger.warning(f"文件为空: {file_path}")
                    return None
            except Exception as e:
                logger.error(f"并行读取文件失败: {file_path}, 错误: {e}")
                return None

        # 使用字典保存结果，确保按原始顺序排列
        file_results = {}

        # 使用线程池并行读取
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(read_single_file, file): file for file in files}

            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    df = future.result()
                    if df is not None:
                        file_results[file_path] = df
                except Exception as e:
                    logger.error(f"获取并行读取结果失败: {file_path}, 错误: {e}")

        # 按原始文件顺序重新排列结果
        dfs = []
        for file_path in files:
            if file_path in file_results:
                dfs.append(file_results[file_path])

        logger.debug(f"并行读取完成，成功读取 {len(dfs)}/{len(files)} 个文件，结果已按文件顺序排列")
        return dfs

    @performance_monitor
    def read_partitioned_data_vectorized(self, data_root: str, symbol: str, period: str,
                                       start_time: Optional[str] = None,
                                       end_time: Optional[str] = None,
                                       columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        向量化读取分区数据（主要接口，完全保持原始数据结构）

        使用Pandas Concat实现的向量化读取，确保：
        - 完全保持原始数据结构和索引
        - 支持多线程并行读取优化
        - 无任何数据修改或转换

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列

        Returns:
            pd.DataFrame: 读取的数据（完全保持原始结构）
        """
        logger.debug(f"向量化读取分区数据: {symbol} {period} {start_time}-{end_time}")
        
        # 更新统计信息
        self._performance_stats['total_calls'] += 1
        
        try:
            # 解析symbol为market和code
            if '.' in symbol:
                code, market = symbol.split('.', 1)
            else:
                # 如果没有市场后缀，尝试从数据目录推断
                code = symbol
                market = None  # 让path_manager自动推断

            # 获取分区文件列表
            partition_files = get_partition_files(
                data_root=data_root,
                market=market,
                code=code,
                period=period,
                start_time=start_time,
                end_time=end_time
            )
            
            if not partition_files:
                logger.warning(f"未找到分区文件: {symbol} {period}")
                return None
            
            logger.debug(f"找到 {len(partition_files)} 个分区文件")
            
            # 使用向量化方式读取文件
            df = self.read_files_vectorized(partition_files, columns)
            
            if df is None or df.empty:
                logger.warning(f"向量化读取结果为空: {symbol} {period}")
                return None

            # 验证最终结果的索引格式
            from utils.data_processor.index_manager import IndexManager
            IndexManager.log_index_info(df, "向量化读取最终结果")

            logger.debug(f"Pandas向量化读取完成: {len(df)} 行数据，完全保持原始数据结构")
            return df
            
        except Exception as e:
            logger.error(f"向量化读取分区数据失败: {e}")
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            dict: 性能统计信息
        """
        stats = self._performance_stats.copy()
        if stats['total_calls'] > 0:
            stats['avg_time_per_call'] = stats['total_time'] / stats['total_calls']
            stats['avg_files_per_call'] = stats['total_files_read'] / stats['total_calls']
            stats['avg_data_size_per_call'] = stats['total_data_size'] / stats['total_calls']
        else:
            stats['avg_time_per_call'] = 0.0
            stats['avg_files_per_call'] = 0.0
            stats['avg_data_size_per_call'] = 0.0
        
        if stats['total_calls'] > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['total_calls']
        else:
            stats['cache_hit_rate'] = 0.0
        
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        if self._cache:
            self._cache.clear()
            logger.debug("缓存已清空")
    
    def reset_performance_stats(self):
        """重置性能统计信息"""
        self._performance_stats = {
            'total_calls': 0,
            'total_time': 0.0,
            'cache_hits': 0,
            'total_files_read': 0,
            'total_data_size': 0
        }
        logger.debug("性能统计信息已重置")
    
    async def read_files_async(self, files: List[str],
                             columns: Optional[List[str]] = None,
                             max_concurrent: int = 5) -> Optional[pd.DataFrame]:
        """
        异步读取多个文件

        Args:
            files: 文件路径列表
            columns: 要读取的列名列表
            max_concurrent: 最大并发数

        Returns:
            pd.DataFrame: 合并后的数据
        """
        if not files:
            return None

        logger.debug(f"异步读取 {len(files)} 个文件，最大并发数: {max_concurrent}")

        async def read_single_file(file_path: str) -> Optional[pd.DataFrame]:
            """异步读取单个文件"""
            loop = asyncio.get_event_loop()
            try:
                # 在线程池中执行文件读取
                df = await loop.run_in_executor(
                    None,
                    lambda: pd.read_parquet(file_path, columns=columns) if os.path.exists(file_path) else None
                )
                if df is not None and not df.empty:
                    logger.debug(f"异步读取文件成功: {file_path}, 行数: {len(df)}")
                    return df
                else:
                    logger.warning(f"异步读取文件为空: {file_path}")
                    return None
            except Exception as e:
                logger.error(f"异步读取文件失败: {file_path}, 错误: {e}")
                return None

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def read_with_semaphore(file_path: str) -> Optional[pd.DataFrame]:
            async with semaphore:
                return await read_single_file(file_path)

        # 并发读取所有文件
        tasks = [read_with_semaphore(file_path) for file_path in files]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        dfs = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"异步读取任务失败: {files[i]}, 错误: {result}")
            elif result is not None:
                dfs.append(result)

        if not dfs:
            logger.warning("异步读取没有成功读取任何文件")
            return None

        # 合并数据 - 使用统一的索引处理标准
        if len(dfs) == 1:
            return dfs[0]
        else:
            logger.debug(f"异步合并 {len(dfs)} 个数据文件")
            # 导入统一索引管理器
            from utils.data_processor.index_manager import IndexManager

            # 使用安全合并，确保索引格式正确
            result_df = IndexManager.safe_concat(dfs, axis=0)

            if result_df is not None:
                logger.debug("异步合并完成，索引格式已验证")
                return result_df
            else:
                logger.error("异步合并失败，使用传统方式合并")
                # 备用方案：使用ignore_index=False
                return pd.concat(dfs, axis=0, ignore_index=False)

    async def read_partitioned_data_async(self, data_root: str, symbol: str, period: str,
                                        start_time: Optional[str] = None,
                                        end_time: Optional[str] = None,
                                        columns: Optional[List[str]] = None,
                                        max_concurrent: int = 5) -> Optional[pd.DataFrame]:
        """
        异步读取分区数据（保持原始数据格式）

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            max_concurrent: 最大并发数

        Returns:
            pd.DataFrame: 读取的数据（保持原始格式）
        """
        logger.debug(f"异步读取分区数据: {symbol} {period} {start_time}-{end_time}")

        try:
            # 解析symbol为market和code
            if '.' in symbol:
                code, market = symbol.split('.', 1)
            else:
                # 如果没有市场后缀，尝试从数据目录推断
                code = symbol
                market = None  # 让path_manager自动推断

            # 获取分区文件列表
            partition_files = get_partition_files(
                data_root=data_root,
                market=market,
                code=code,
                period=period,
                start_time=start_time,
                end_time=end_time
            )

            if not partition_files:
                logger.warning(f"未找到分区文件: {symbol} {period}")
                return None

            logger.debug(f"找到 {len(partition_files)} 个分区文件")

            # 异步读取文件
            df = await self.read_files_async(partition_files, columns, max_concurrent)

            if df is None or df.empty:
                logger.warning(f"异步读取结果为空: {symbol} {period}")
                return None

            logger.debug(f"异步读取完成: {len(df)} 行数据，保持原始数据格式")
            return df

        except Exception as e:
            logger.error(f"异步读取分区数据失败: {e}")
            return None

    def __del__(self):
        """析构函数，清理资源"""
        # Pandas实现无需特殊清理
        pass


# 全局实例，避免重复初始化
_global_reader = None

def get_vectorized_reader() -> VectorizedDataReader:
    """
    获取全局向量化数据读取器实例
    
    Returns:
        VectorizedDataReader: 向量化数据读取器实例
    """
    global _global_reader
    if _global_reader is None:
        _global_reader = VectorizedDataReader()
    return _global_reader


class StreamingDataReader:
    """
    流式数据读取器

    支持大数据集的流式处理，避免内存溢出
    """

    def __init__(self, chunk_size: int = 10000):
        """
        初始化流式数据读取器

        Args:
            chunk_size: 每个数据块的大小
        """
        self.chunk_size = chunk_size
        self.vectorized_reader = get_vectorized_reader()

    def read_partitioned_data_streaming(self, data_root: str, symbol: str, period: str,
                                      start_time: Optional[str] = None,
                                      end_time: Optional[str] = None,
                                      columns: Optional[List[str]] = None):
        """
        流式读取分区数据

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列

        Yields:
            pd.DataFrame: 数据块
        """
        logger.debug(f"流式读取分区数据: {symbol} {period} {start_time}-{end_time}")

        try:
            # 获取分区文件列表
            partition_files = get_partition_files(
                data_root=data_root,
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time
            )

            if not partition_files:
                logger.warning(f"未找到分区文件: {symbol} {period}")
                return

            logger.debug(f"流式处理 {len(partition_files)} 个分区文件")

            # 逐个文件流式处理
            for file_path in partition_files:
                try:
                    if os.path.exists(file_path):
                        # 使用pandas的chunksize参数进行流式读取
                        for chunk in pd.read_parquet(file_path, columns=columns, chunksize=self.chunk_size):
                            if chunk is not None and not chunk.empty:
                                logger.debug(f"流式读取数据块: {len(chunk)} 行")
                                yield chunk
                            else:
                                logger.debug(f"跳过空数据块: {file_path}")
                    else:
                        logger.warning(f"文件不存在: {file_path}")
                except Exception as e:
                    logger.error(f"流式读取文件失败: {file_path}, 错误: {e}")
                    continue

        except Exception as e:
            logger.error(f"流式读取分区数据失败: {e}")
            return

    def process_streaming_data(self, data_root: str, symbol: str, period: str,
                             processor_func,
                             start_time: Optional[str] = None,
                             end_time: Optional[str] = None,
                             columns: Optional[List[str]] = None):
        """
        流式处理数据

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            processor_func: 数据处理函数
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
        """
        logger.debug(f"开始流式处理数据: {symbol} {period}")

        processed_count = 0
        for chunk in self.read_partitioned_data_streaming(
            data_root, symbol, period, start_time, end_time, columns
        ):
            try:
                # 应用处理函数
                processed_chunk = processor_func(chunk)
                processed_count += len(chunk)

                logger.debug(f"已处理 {processed_count} 行数据")

                # 可以在这里保存处理结果或进行其他操作
                yield processed_chunk

            except Exception as e:
                logger.error(f"流式处理数据块失败: {e}")
                continue

        logger.debug(f"流式处理完成，总计处理 {processed_count} 行数据")


def read_partitioned_data_vectorized(data_root: str, symbol: str, period: str,
                                   start_time: Optional[str] = None,
                                   end_time: Optional[str] = None,
                                   columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
    """
    向量化读取分区数据（便捷函数，完全保持原始数据结构）

    使用Pandas Concat实现，确保完全保持数据原始性。

    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        start_time: 开始时间
        end_time: 结束时间
        columns: 要读取的列

    Returns:
        pd.DataFrame: 读取的数据（完全保持原始结构）
    """
    reader = get_vectorized_reader()
    return reader.read_partitioned_data_vectorized(
        data_root, symbol, period, start_time, end_time, columns
    )


async def read_partitioned_data_async(data_root: str, symbol: str, period: str,
                                    start_time: Optional[str] = None,
                                    end_time: Optional[str] = None,
                                    columns: Optional[List[str]] = None,
                                    max_concurrent: int = 5) -> Optional[pd.DataFrame]:
    """
    异步读取分区数据（便捷函数，保持原始数据格式）

    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        start_time: 开始时间
        end_time: 结束时间
        columns: 要读取的列
        max_concurrent: 最大并发数

    Returns:
        pd.DataFrame: 读取的数据（保持原始格式）
    """
    reader = get_vectorized_reader()
    return await reader.read_partitioned_data_async(
        data_root, symbol, period, start_time, end_time, columns, max_concurrent
    )
