#!/usr/bin/env python
# -*- coding: utf-8 -*-

import datetime
import pandas as pd
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入日志模块
from utils.logger import get_unified_logger, LogTarget

# 初始化日志记录器
logger = get_unified_logger(__name__, enhanced=True)




# 时间戳转换示例
timestamp_ms = 1750294801500 
dt = datetime.datetime.fromtimestamp(timestamp_ms / 1000)
print(dt)

pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.width', None)  # 设置宽度为无限制
pd.set_option('display.max_rows', None)  # 显示所有行

# 读取并合并多个tick数据文件
file_paths = [
    'D:/data/SZ/002594/tick/2025/07/21.parquet',
    #'D:/data/SF/rb00/tick/2025/07/02.parquet',
    #'D:/data/SF/rb00/tick/2025/07/03.parquet'
]

# 读取所有文件并存储到列表中
dfs = []
for file_path in file_paths:
    try:
        df_temp = pd.read_parquet(file_path)
        logger.info(f"成功读取 {file_path}，数据行数: {len(df_temp)}")
        dfs.append(df_temp)
    except Exception as e:
        logger.error(f"读取文件失败 {file_path}: {e}")

# 合并所有数据
if dfs:
    df = pd.concat(dfs, ignore_index=False)  # 保持原始索引
    logger.info(f"合并完成，总数据行数: {len(df)}")

    # 按时间索引排序（如果需要）
    df = df.sort_index()
    logger.info(f"数据时间范围: {df.index[0]} 到 {df.index[-1]}")
else:
    logger.error("没有成功读取任何数据文件")
    df = pd.DataFrame()

#logger.info(f"tick数据:\n{df.loc['20250625145550':'20250625150003'] }")
#logger.info(f"tick数据:\n{df.loc['20250701112930':'20250701133030'] }")
#logger.info(f"tick数据:\n{df.loc['20250701145930':'20250701210030'] }")
logger.info(f"tick数据:\n{df.loc['20250721145500':'20250721210500'] }")

# 读取并显示1m数据（可选）
#df_1m = pd.read_parquet('D:/data/ZF/fg00/1m/2025.parquet')
#logger.info(f"1m数据:\n{df_1m.head(1000)}")
df_1m = pd.read_parquet('D:/data/SZ/002594/1m/2025.parquet')
#logger.info(f"1m数据:\n{df_1m.loc['20250720093000':'20250720150000']}")
logger.info(f"1m数据:\n{df_1m.loc['20250721000000':'20250721240000']}")

# 读取并显示1d数据
# df = pd.read_parquet('D:/data/SZ/002007/1d/2024.parquet')
# logger.info(f"1d数据：\n{df}")



