# 工具模块 (Utils)

> **重要要求**：
> 1.在开发开发功能时，优先使用通用模块中的通用函数。
> 2.通用模块内没有通用功能时，开发的功能函数首先考虑开发成通用函数，并且放入通用模块中，作为通用功能供其他模块使用，避免项目代码大量重复。
> 3.生成和修改的通用模块不得超过500行，尽量保持模块粒度最小化，以便管理，这样以后调整项目结构不用大拆大修，以免影响到其他模块使用，又得重构代码。

## 🎉 新增：智能时间转换器 (2025-01-18)

### 时间处理核心模块
- `smart_time_converter.py` - **智能时间转换器核心**，专为金融数据设计
- `time_converter_config.py` - 时间转换器配置管理
- `time_converter_manager.py` - 统一管理和监控
- `time_utils.py` - 时间处理工具函数

**核心特性**：
- ✅ 彻底解决pd.to_datetime的8小时时区偏移问题
- ✅ 智能类型检测（毫秒/秒/字符串）
- ✅ 严格验证，避免时区风险
- ✅ 统一管理，一处修改影响全项目
- ⚠️ **不使用pd.to_datetime后备方案**，确保金融数据时间准确性

**快速使用**：
```python
from utils.smart_time_converter import smart_to_datetime

# 推荐：明确指定时间戳类型
result = smart_to_datetime([1737158400000], unit='ms')  # 毫秒时间戳

# 推荐：明确指定字符串格式
result = smart_to_datetime(['20250118080000'], format='%Y%m%d%H%M%S')

# ⚠️ 避免：模糊格式可能导致TimeConversionError
# result = smart_to_datetime(['2025'])  # 会抛出错误，要求明确格式
```

## 模块开发规范

本目录包含量化交易系统中的各种通用工具，用于支持项目中的各个模块。这些工具模块提供了日志记录、性能指标计算、数据展示、文本格式化、文本解析、路径处理和输入处理等功能。

## 模块概览

### 核心模块

| 模块名 | 描述 |
|-----|-----|
| `metrics.py`          | 金融和回测性能指标计算工具       |
| `data_display/`       | 数据展示与文本格式化工具模块     |
| `input_handler.py`    | 用户输入处理工具                 |
| `stock_utils.py`      | 股票相关工具函数                 |
| `factory_pattern.py`  | 工厂模式实现工具                 |

### 子模块目录

| 子模块目录 | 描述 |
|-----|-----|
| `logger/`             | 日志记录工具模块                 |
| `path_utils/`         | 路径处理与文件操作工具模块       |
| `data_processor/`     | 数据处理与清洗工具模块           |
| `time_formatter/`     | 时间处理和格式化工具模块         |


## 日志记录工具 (logger)

日志模块提供了完整的日志记录、配置和处理功能，包括：

- `config.py`: 日志配置功能
- `core.py`: 日志核心功能
- `handlers.py`: 日志处理器
- `decorators.py`: 日志装饰器
- `formatters.py`: 日志格式化器

## 路径处理工具 (path_utils)

路径工具模块提供文件和目录操作、路径管理功能：

- `file_operations.py`: 文件操作功能
- `directory_operations.py`: 目录操作功能
- `path_management.py`: 路径管理功能

## 数据处理工具 (data_processor)

数据处理模块提供数据清洗、转换、验证和准备功能：

- `cleaning.py`: 数据清洗功能
- `outliers.py`: 异常值处理功能
- `transformation.py`: 数据转换功能
- `validation.py`: 数据验证功能
- `technical.py`: 技术指标计算功能
- `preparation.py`: 数据准备功能

## 数据展示工具 (data_display)

数据展示模块提供统一的数据格式化和显示功能，用于在控制台和日志中美观地展示各种数据结构：

- `table.py`: 表格显示功能，包含将DataFrame转换为文本表格的核心函数
- `formatting.py`: 数据格式化功能，提供各种数据格式化函数
- `text.py`: 文本处理功能，提供文本美化和处理函数
- `console.py`: 控制台显示功能，提供在控制台中显示数据的函数
- `menus.py`: 菜单显示功能，提供交互式菜单的创建和显示

主要功能包括：
- DataFrame转换为文本表格
- 数值格式化（智能浮点数、百分比等）
- 文本美化（边框、高亮等）
- 控制台交互（进度条、菜单等）

## 时间格式化工具 (time_formatter)

时间处理模块提供时间戳转换、格式化、标准化和解析功能：

- `conversion.py`: 时间转换功能
- `formatting.py`: 时间格式化功能
- `parsing.py`: 时间解析功能
- `validation.py`: 日期验证功能

## 任务检查点管理工具 (task_checkpoint.py)

`task_checkpoint.py` 提供了一个自动化工具，用于记录和管理项目中的任务状态、进度和计划。该工具可以更新任务状态文档(`TASK_STATUS.md`)并创建Git检查点，有助于在复杂项目开发中保持任务连续性，尤其是在会话中断或切换时。

### 主要功能

1. **读取任务状态**：读取`TASK_STATUS.md`文件的内容，解析为结构化数据。
2. **更新任务状态**：根据用户提供的信息，更新任务状态文档的各个部分。
3. **创建Git检查点**：自动添加修改的文件并创建带有时间戳的Git提交。
4. **命令行接口**：提供便捷的命令行接口，支持各种参数。

### 使用方法

#### 基本使用

```bash
python -m utils.task_checkpoint "检查点消息"
```

这将使用默认参数创建一个检查点，更新任务状态文档并创建Git提交。

#### 高级用法

```bash
python -m utils.task_checkpoint "检查点消息" \
  --completed "完成的任务1" "完成的任务2" \
  --added "新增任务1" "新增任务2" \
  --next "下一步行动1" "下一步行动2" \
  --files "修改的文件1" "修改的文件2" \
  --notes "特别说明内容"
```

#### 参数说明

- `message`：检查点消息（必需）
- `--completed`：完成的任务列表（可多个）
- `--added`：新增的任务列表（可多个）
- `--next`：下一步行动列表（可多个）
- `--files`：修改的文件列表（可多个）
- `--notes`：特别说明内容（可选）
- `--dry-run`：仅显示将要执行的操作，不实际执行（默认：False）
- `--help`：显示帮助信息

### 工作流示例

1. **开始新任务**：
   ```bash
   python -m utils.task_checkpoint "开始实现新功能" \
     --added "实现功能A" "实现功能B" "编写单元测试" \
     --next "设计功能A的API" \
     --notes "优先完成功能A"
   ```

2. **记录完成的子任务**：
   ```bash
   python -m utils.task_checkpoint "完成API设计" \
     --completed "设计功能A的API" \
     --next "实现功能A" \
     --files "docs/api_design.md" "src/feature_a.py"
   ```

3. **创建会话中断检查点**：
   ```bash
   python -m utils.task_checkpoint "会话中断检查点" \
     --completed "实现功能A的核心逻辑" \
     --next "完成功能A的边界情况处理" \
     --files "src/feature_a.py" "tests/test_feature_a.py"
   ```

### 最佳实践

1. **定期创建检查点**：尤其是在完成关键功能或即将中断会话时。
2. **详细记录任务状态**：包括已完成任务、下一步行动和修改的文件。
3. **添加有意义的检查点消息**：清晰描述当前进度和状态。
4. **会话开始时查看任务状态**：通过查看`TASK_STATUS.md`了解上一次会话的进度和计划。
5. **使用Git历史记录**：检查点会创建Git提交，可以通过查看Git历史了解项目进展。

### 注意事项

- 工具会自动在检查点消息中添加时间戳。
- 若`TASK_STATUS.md`不存在，工具会自动创建包含基本结构的文件。
- 检查点创建成功后会显示确认消息。

## 工作流程测试工具 (test_workflow.py)

`test_workflow.py` 是一个简单的工具模块，用于验证AI助手工作流程框架的正常执行。它演示了如何使用任务检查点功能，确保工作流程中的各个步骤按照预期执行。

### 主要功能

1. **工作流程测试函数**：提供了一个带自动检查点的示例函数，用于验证检查点创建功能。
2. **模拟任务执行**：模拟实际任务执行过程中的等待和日志记录功能。
3. **结果反馈**：提供标准化的结果输出，便于验证流程执行状态。

### 使用方法

```bash
python -m utils.test_workflow
```

这将执行默认的工作流程测试，创建检查点并输出结果。

### 自定义测试

```python
from utils.test_workflow import run_workflow_test

# 自定义测试名称，运行测试
run_workflow_test("自定义测试名称")
```

### 最佳实践

1. **验证功能完整性**：使用此工具验证任务检查点和工作流程框架的完整功能。
2. **开发新功能前测试**：在开发新功能前，可以先运行工作流程测试，确保基础框架正常工作。
3. **问题排查**：当怀疑工作流程或检查点功能异常时，可以运行此测试进行快速验证。 