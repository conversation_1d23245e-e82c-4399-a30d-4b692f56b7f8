
from xtquant import xtdata as xt_data
def download_data():
    xt_data.download_history_data2(
        stock_list=["002493.SZ"],   
        period="1m",
        start_time="20250721093000",
        end_time="20250721150000"
    )

    result = xt_data.get_local_data(
        field_list=[],
        stock_list=["002493.SZ"],
        period="1m",
        start_time="20250721093000",
        end_time="20250721150000",
        dividend_type="front"
    )

    return result

if __name__ == "__main__":
    import pandas as pd
    pd.set_option('display.max_columns', None)  # 显示所有列
    pd.set_option('display.width', None)  # 设置宽度为无限制
    pd.set_option('display.max_rows', None)  # 显示所有行
    result = download_data()
    df = pd.DataFrame(result["002493.SZ"])
    #print(df.head(300))
    print(df.loc['20250721093000':'20250721150000'])
