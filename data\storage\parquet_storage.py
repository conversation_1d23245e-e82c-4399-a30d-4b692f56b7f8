#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化版 Parquet 数据存储模块

基于原始版本优化性能:
1. 减少过度的日志记录
2. 简化数据转换过程
3. 优化PyArrow表创建
4. 精简元数据处理
5. 合理化错误处理
6. 优化数据转换和分组
7. 优化并行处理
8. 优化文件操作
"""

import os
import time
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple, Union
import concurrent.futures

import numpy as np
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq

# 导入路径管理模块
from data.storage.path_manager import (
    get_partitioned_path,
    get_latest_partition_file
)

# 导入配置项
from config.settings import (
    ENABLE_PARALLEL_STORAGE,
    STORAGE_MAX_WORKERS,
    STORAGE_USE_THREADING,
    ENABLE_GLOBAL_PROCESS_POOL,
    GLOBAL_POOL_MAX_WORKERS,
    GLOBAL_POOL_CHUNKSIZE
)

# 使用增强型日志记录器
from utils.logger import get_unified_logger, LogTarget

# 导入智能时间转换器
from utils.smart_time_converter import smart_to_datetime

# 创建日志记录器
logger = get_unified_logger(__name__, enhanced=True)

# 导入全局进程池（如果启用）
_use_global_pool = ENABLE_GLOBAL_PROCESS_POOL
if _use_global_pool:
    try:
        from utils.multiprocessing.global_process_pool import GlobalProcessPool
        logger.info(LogTarget.FILE, "已加载全局进程池模块")
    except ImportError:
        logger.warning(LogTarget.FILE, "无法加载全局进程池模块，将使用标准并行处理")
        _use_global_pool = False


def save_data_to_parquet(
    df: pd.DataFrame, 
    file_path: str,
    engine: str = 'pyarrow',
    metadata: Optional[Dict[str, Any]] = None,
    create_dir: bool = True,
    **kwargs
) -> bool:
    """
    将 DataFrame 保存到 Parquet 文件

    Args:
        df: 需要保存的数据帧
        file_path: 目标 Parquet 文件的完整路径
        engine: 使用的 Parquet 引擎 ('pyarrow' 或 'fastparquet')
        metadata: 要保存的元数据字典
        create_dir: 是否创建目录，默认为True
        **kwargs: 其他传递给 df.to_parquet 的参数 (例如: compression='snappy')

    Returns:
        bool: 保存成功返回True，失败返回False
    """
    try:
        # 确保目录存在
        if create_dir:
            directory = os.path.dirname(file_path)
            if directory:
                os.makedirs(directory, exist_ok=True)

        # 准备精简的元数据
        meta = {
            'created_at': datetime.now().isoformat(),
            'rows_count': str(len(df)),
        }

        # 合并用户提供的元数据
        if metadata:
            meta.update(metadata)

        # 确保所有元数据值都是字符串
        meta = {k: str(v) for k, v in meta.items()}

        if engine == 'pyarrow':
            try:
                # 获取压缩方式
                compression = kwargs.get('compression', 'snappy')
                
                # 确保保存索引
                preserve_index = kwargs.get('index', True)
                
                # 根据数据特性选择最优的转换方法
                table = optimize_numeric_table_creation(
                    df, preserve_index=preserve_index
                )
                
                # 添加元数据
                metadata_dict = {}
                if table.schema.metadata:
                    metadata_dict.update(table.schema.metadata)
                
                # 添加自定义元数据（简化处理）
                for k, v in meta.items():
                    metadata_dict[k.encode('utf-8')] = v.encode('utf-8')
                
                # 更新元数据
                table = table.replace_schema_metadata(metadata_dict)
                
                # 写入到Parquet文件
                pq.write_table(table, file_path, compression=compression)
                
                logger.debug(LogTarget.FILE, "数据成功保存到: {}".format(file_path))
                return True
                
            except Exception as e:
                logger.error(LogTarget.FILE, "使用PyArrow保存数据失败: {}".format(e))
                # 回退到pandas直接保存，确保保存索引
                df.to_parquet(file_path, index=True, **kwargs)
                logger.debug(LogTarget.FILE, "使用pandas接口直接保存数据到: {}".format(file_path))
                return True
        else:
            # 使用pandas直接保存，确保保存索引
            df.to_parquet(file_path, engine=engine, index=True, **kwargs)
            logger.debug(LogTarget.FILE, "使用{}引擎保存数据到: {}".format(engine, file_path))
            return True
            
    except Exception as e:
        logger.error(LogTarget.FILE, "保存数据到Parquet文件失败: {}".format(e))
        return False


def get_parquet_metadata(file_path: str) -> Dict[str, Any]:
    """
    读取Parquet文件的元数据

    Args:
        file_path: Parquet文件路径

    Returns:
        Dict[str, Any]: 元数据字典
    """
    try:
        metadata = {}
        
        if not os.path.exists(file_path):
            logger.error(LogTarget.FILE, "文件不存在: {}".format(file_path))
            return metadata
            
        # 使用PyArrow读取元数据
        table = pq.read_table(file_path, columns=[])
        raw_metadata = table.schema.metadata
        
        if raw_metadata:
            for key, value in raw_metadata.items():
                try:
                    # 尝试解码为字符串
                    k = key.decode('utf-8') if isinstance(key, bytes) else key
                    v = value.decode('utf-8') if isinstance(value, bytes) else value
                    metadata[k] = v
                except Exception as e:
                    # 如果解码失败，使用原始值
                    metadata[key] = value
                    logger.debug(LogTarget.FILE, "元数据解码失败: {}".format(e))
                    
        return metadata
        
    except Exception as e:
        logger.error(LogTarget.FILE, "读取Parquet元数据失败: {}".format(e))
        return {}


def save_to_partition(
    df: pd.DataFrame,
    data_root: str,
    symbol: str,
    period: str,
    timestamp: Optional[str] = None,
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    将数据保存到指定分区

    Args:
        df: 要保存的数据
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        timestamp: 时间戳，用于确定分区路径
        engine: 使用的引擎，'pyarrow'或'fastparquet'
        compression: 压缩方式
        metadata: 元数据

    Returns:
        bool: 是否成功保存
    """
    try:
        rows_count = len(df)
        # 只有当数据量大于10行时才记录详细日志
        verbose_logging = rows_count > 10
        
        if verbose_logging:
            logger.debug(
                LogTarget.FILE, 
                "开始保存 {} 的 {} 数据到分区 ({} 行)".format(symbol, period, rows_count)
            )
        
        # 添加时间范围信息
        if not df.empty and verbose_logging:
            # 查找可能的时间列
            for col in ['time', 'datetime', 'date', 'timestamp']:
                if col in df.columns:
                    if pd.api.types.is_datetime64_any_dtype(df[col]):
                        min_time = df[col].min()
                        max_time = df[col].max()
                        logger.debug(
                            LogTarget.FILE,
                            "数据时间范围: {} 至 {}".format(min_time, max_time)
                        )
                    elif pd.api.types.is_numeric_dtype(df[col]):
                        min_time = df[col].min()
                        max_time = df[col].max()
                        logger.debug(
                            LogTarget.FILE,
                            "数据时间戳范围: {} 至 {}".format(min_time, max_time)
                        )
                    break

        # 获取分区路径
        partition_path = get_partitioned_path(data_root, symbol, period, timestamp)
        
        # 确保目录存在
        directory = os.path.dirname(partition_path)
        os.makedirs(directory, exist_ok=True)
        
        # 检查文件是否已存在
        if os.path.exists(partition_path):
            if verbose_logging:
                logger.debug(LogTarget.FILE, "文件已存在，将覆盖: {}".format(partition_path))
            
            # 直接覆盖现有文件，不进行合并
            result = save_data_to_parquet(
                df,
                partition_path,
                engine=engine,
                compression=compression,
                metadata=metadata
            )
            
            if result:
                # 获取文件大小
                file_size = os.path.getsize(partition_path)
                file_size_kb = file_size / 1024
                
                # 拆分长日志行
                if verbose_logging:
                    log_msg = "已保存 {} 的 {} 数据: {} ".format(
                        symbol, period, partition_path
                    )
                    log_msg += "({} 行, {:.1f} KB)\n".format(rows_count, file_size_kb)
                    logger.debug(LogTarget.FILE, log_msg)
            return True
        else:
            # 文件不存在，直接保存
            if verbose_logging:
                logger.debug(LogTarget.FILE, "文件不存在，直接保存: {}".format(partition_path))
            
            result = save_data_to_parquet(
                df,
                partition_path,
                engine=engine,
                compression=compression,
                metadata=metadata
            )
            
            if result:
                # 获取文件大小
                file_size = os.path.getsize(partition_path)
                file_size_kb = file_size / 1024
                
                # 拆分长日志行
                if verbose_logging:
                    log_msg = "已保存 {} 的 {} 数据: {} ".format(
                        symbol, period, partition_path
                    )
                    log_msg += "({} 行, {:.1f} KB)\n".format(rows_count, file_size_kb)
                    logger.debug(LogTarget.FILE, log_msg)
                return True
            else:
                return False
                
    except Exception as e:
        logger.error(LogTarget.FILE, "保存分区数据时出错: {}".format(e))
        return False


def save_data_by_partition(
    df: pd.DataFrame, 
    data_root: str, 
    symbol: str, 
    period: str, 
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    metadata: Optional[Dict[str, Any]] = None,
    parallel: Optional[bool] = None,
    use_threading: Optional[bool] = None,
    max_workers: Optional[int] = None
) -> Dict[str, str]:
    """
    按日期分区保存数据，支持并行处理

    Args:
        df: 要保存的数据
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        engine: 使用的引擎
        compression: 压缩方式
        metadata: 元数据
        parallel: 是否使用并行处理，默认使用配置
        use_threading: 是否使用多线程，如果False则使用多进程
        max_workers: 最大工作线程/进程数

    Returns:
        Dict[str, str]: 保存结果，键为分区标识，值为保存路径
    """
    # 使用配置或传入的参数
    parallel = ENABLE_PARALLEL_STORAGE if parallel is None else parallel
    use_threading = STORAGE_USE_THREADING if use_threading is None else use_threading
    max_workers = STORAGE_MAX_WORKERS if max_workers is None else max_workers
    
    if df is None or df.empty:
        logger.warning(LogTarget.FILE, "数据为空，无法保存")
        return {}

    try:
        # 查找时间列
        time_col = None
        for col in ['time', 'datetime', 'date', 'timestamp']:
            if col in df.columns:
                time_col = col
                break
                
        if time_col is None:
            logger.error(LogTarget.FILE, "未找到时间列，无法按日期分区")
            return {}
            
        # 准备任务
        tasks = []
        
        # 对于tick数据，按日期分组；对于其他周期，按年份分组
        if period.lower() == 'tick':
            # 创建临时Series用于分组，而不是添加到DataFrame
            if pd.api.types.is_numeric_dtype(df[time_col]):
                # 时间戳格式，转换为datetime
                datetime_index = smart_to_datetime(df[time_col], unit='ms')
                date_series = datetime_index.date if hasattr(datetime_index, 'date') else datetime_index.dt.date
            elif pd.api.types.is_datetime64_any_dtype(df[time_col]):
                # 已经是datetime格式
                date_series = df[time_col].dt.date
            else:
                # 尝试解析字符串格式
                datetime_index = smart_to_datetime(df[time_col])
                date_series = datetime_index.date if hasattr(datetime_index, 'date') else datetime_index.dt.date
            
            # 按日期分组
            groups = df.groupby(date_series)
            
            # 准备任务
            for date, group_data in groups:
                # 转换日期为字符串
                date_str = date.strftime('%Y%m%d')
                
                # 添加到任务列表（不需要删除临时列，因为没有创建）
                tasks.append((
                    group_data, data_root, symbol, period, 
                    date_str, engine, compression, metadata
                ))
        else:
            # 对于非tick数据，按年份分组
            if pd.api.types.is_numeric_dtype(df[time_col]):
                # 时间戳格式，转换为datetime
                datetime_index = smart_to_datetime(df[time_col], unit='ms')
                year_series = datetime_index.year if hasattr(datetime_index, 'year') else datetime_index.dt.year
            elif pd.api.types.is_datetime64_any_dtype(df[time_col]):
                # 已经是datetime格式
                year_series = df[time_col].dt.year
            else:
                # 尝试解析字符串格式
                datetime_index = smart_to_datetime(df[time_col])
                year_series = datetime_index.year if hasattr(datetime_index, 'year') else datetime_index.dt.year
            
            # 按年份分组
            groups = df.groupby(year_series)
            
            # 准备任务
            for year, group_data in groups:
                # 转换年份为标准8位日期格式（YYYYMMDD），使用年份的第一天
                year_date_str = f"{year}0101"

                # 添加到任务列表
                tasks.append((
                    group_data, data_root, symbol, period,
                    year_date_str, engine, compression, metadata
                ))
        
        # 优化任务分配，按数据量平衡任务
        balanced_tasks = balance_tasks_by_size(tasks)
        
        # 根据配置选择处理方式
        results = {}
        if parallel and max_workers > 1:
            # 检查是否使用全局进程池
            use_global_pool = _use_global_pool and not use_threading
            
            if use_global_pool:
                logger.info(
                    LogTarget.FILE, 
                    f"使用全局进程池保存数据，工作进程数: {GLOBAL_POOL_MAX_WORKERS}"
                )
                
                try:
                    # 获取全局进程池实例
                    pool = GlobalProcessPool.get_instance(GLOBAL_POOL_MAX_WORKERS)
                    
                    # 检查进程池是否已经初始化或已关闭
                    if not pool._pool:
                        logger.info(LogTarget.FILE, "全局进程池已关闭，正在重新初始化...")
                        # 重置单例状态以允许重新初始化
                        GlobalProcessPool._initialized = False
                        GlobalProcessPool._instance = None
                        # 重新获取实例
                        pool = GlobalProcessPool.get_instance(GLOBAL_POOL_MAX_WORKERS)
                        logger.info(LogTarget.FILE, "全局进程池已重新初始化")
                    
                    # 准备任务参数列表
                    task_args = []
                    for task in balanced_tasks:
                        task_args.append((
                            task[0],  # group_data
                            task[1],  # data_root
                            task[2],  # symbol
                            task[3],  # period
                            task[4],  # date_str/year_str
                            task[5],  # engine
                            task[6],  # compression
                            task[7]   # metadata
                        ))
                    
                    # 使用全局进程池执行任务
                    start_time = time.time()
                    pool_results = pool.map(
                        save_to_partition_wrapper, 
                        task_args,
                        chunksize=GLOBAL_POOL_CHUNKSIZE
                    )
                    elapsed_time = time.time() - start_time
                    
                    # 处理结果
                    success_count = 0
                    for i, success in enumerate(pool_results):
                        if success:
                            date_str = balanced_tasks[i][4]
                            partition_path = get_partitioned_path(
                                data_root, symbol, period, date_str
                            )
                            results[date_str] = partition_path
                            success_count += 1
                    
                    # 记录完成信息
                    msg = (
                        f"全局进程池保存完成，成功: {success_count}/{len(balanced_tasks)}，"
                        f"耗时: {elapsed_time:.2f}秒"
                    )
                    logger.info(LogTarget.FILE, msg)
                    
                except Exception as e:
                    logger.error(LogTarget.FILE, f"使用全局进程池时出错: {e}")
                    logger.info(LogTarget.FILE, "回退到标准并行处理")
                    # 回退到标准并行处理
                    use_global_pool = False
            
            # 如果全局进程池不可用或出错，使用标准并行处理
            if not use_global_pool:
                # 并行处理逻辑
                logger.info(
                    LogTarget.FILE, 
                    f"使用标准并行处理保存数据，线程数: {max_workers}"
                )
                executor_class = concurrent.futures.ThreadPoolExecutor if use_threading else concurrent.futures.ProcessPoolExecutor
                
                with executor_class(max_workers=max_workers) as executor:
                    # 提交所有任务
                    future_to_date = {}
                    for task in balanced_tasks:
                        future = executor.submit(
                            save_to_partition, 
                            task[0], task[1], task[2], task[3], 
                            task[4], task[5], task[6], task[7]
                        )
                        future_to_date[future] = task[4]  # date_str
                    
                    # 处理结果
                    for future in concurrent.futures.as_completed(future_to_date):
                        date_str = future_to_date[future]
                        try:
                            success = future.result()
                            if success:
                                partition_path = get_partitioned_path(
                                    data_root, symbol, period, date_str
                                )
                                results[date_str] = partition_path
                                logger.debug(LogTarget.FILE, f"并行任务完成: {date_str}")
                        except Exception as e:
                            logger.error(
                                LogTarget.FILE, 
                                f"并行任务失败 {date_str}: {e}"
                            )
                
                logger.info(
                    LogTarget.FILE, 
                    f"标准并行保存完成，成功保存 {len(results)} 个分区"
                )
        else:
            # 顺序处理逻辑
            logger.info(LogTarget.FILE, "使用顺序处理保存数据")
            for task in tasks:
                group_data, data_root, symbol, period, date_str, engine, compression, metadata = task
                
                # 保存到分区
                success = save_to_partition(
                    group_data,
                    data_root,
                    symbol,
                    period,
                    timestamp=date_str,
                    engine=engine,
                    compression=compression,
                    metadata=metadata
                )
                
                if success:
                    partition_path = get_partitioned_path(data_root, symbol, period, date_str)
                    results[date_str] = partition_path
            
            logger.info(LogTarget.FILE, f"顺序保存完成，成功保存 {len(results)} 个分区")
                
        return results
        
    except Exception as e:
        logger.error(LogTarget.FILE, f"保存数据时出错: {e}")
        return {}


def save_to_partition_wrapper(args):
    """
    save_to_partition函数的包装器，用于全局进程池

    Args:
        args: 参数元组 (df, data_root, symbol, period, timestamp, engine, compression, metadata)

    Returns:
        bool: 是否成功保存
    """
    try:
        df, data_root, symbol, period, timestamp, engine, compression, metadata = args
        return save_to_partition(
            df, data_root, symbol, period, timestamp, engine, compression, metadata
        )
    except Exception as e:
        logger.error(LogTarget.FILE, f"保存分区数据时出错: {e}")
        return False


def append_to_partition(
    df: pd.DataFrame,
    data_root: str,
    symbol: str,
    period: str,
    timestamp: Optional[str] = None,
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    将数据追加到分区文件中

    Args:
        df: 要追加的数据
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        timestamp: 时间戳，用于确定分区路径
        engine: 使用的引擎，'pyarrow'或'fastparquet'
        compression: 压缩方式
        metadata: 元数据

    Returns:
        bool: 是否成功追加
    """
    # 为了保持API兼容性，我们将append_to_partition映射到save_to_partition
    logger.info(LogTarget.FILE, "使用save_to_partition替代append_to_partition")
    return save_to_partition(
        df,
        data_root,
        symbol,
        period,
        timestamp,
        engine,
        compression,
        metadata
    )


def incremental_update(
    df: pd.DataFrame,
    data_root: str,
    symbol: str,
    period: str,
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    增量更新数据

    Args:
        df: 要更新的数据
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        engine: 使用的引擎
        compression: 压缩方式
        metadata: 元数据

    Returns:
        bool: 是否成功更新
    """
    # 为了保持API兼容性，我们将incremental_update映射到save_data_by_partition
    logger.info(LogTarget.FILE, "使用save_data_by_partition替代incremental_update")
    results = save_data_by_partition(
        df,
        data_root,
        symbol,
        period,
        engine,
        compression,
        metadata
    )
    return len(results) > 0


def balance_tasks_by_size(tasks):
    """
    优化任务分配，按数据量平衡任务

    Args:
        tasks: 任务列表，每个任务是一个元组 (group_data, data_root, symbol, period, 
                date_str, engine, compression, metadata)

    Returns:
        list: 重新排序的任务列表
    """
    if not tasks:
        return tasks
        
    # 按数据量排序任务
    sorted_tasks = sorted(tasks, key=lambda x: len(x[0]), reverse=True)
    
    # 使用贪心算法平衡任务
    balanced_tasks = []
    task_sizes = []
    
    # 首先添加最大的任务
    for task in sorted_tasks:
        # 如果是第一个任务，直接添加
        if not balanced_tasks:
            balanced_tasks.append(task)
            task_sizes.append(len(task[0]))
            continue
            
        # 找到当前任务大小最小的位置
        min_size_idx = task_sizes.index(min(task_sizes))
        
        # 在该位置之后插入当前任务
        balanced_tasks.insert(min_size_idx + 1, task)
        task_sizes.insert(min_size_idx + 1, len(task[0]))
    
    return balanced_tasks


def optimize_numeric_table_creation(df: pd.DataFrame, preserve_index: bool = True) -> pa.Table:
    """
    根据数据特性优化PyArrow表创建

    Args:
        df: 输入的DataFrame
        preserve_index: 是否保留索引，默认为True

    Returns:
        pa.Table: 优化后的PyArrow表
    """
    try:
        # 检查是否有大量数值型列
        numeric_cols = [
            col for col in df.columns 
            if pd.api.types.is_numeric_dtype(df[col])
        ]
        if len(numeric_cols) > len(df.columns) * 0.7:  # 如果超过70%的列是数值型
            # 使用更高效的方式创建表，但确保保留索引
            return pa.Table.from_pandas(df, preserve_index=preserve_index)
        else:
            # 对于非主要数值型数据，使用标准方法并保留索引
            return pa.Table.from_pandas(df, preserve_index=preserve_index)
    except Exception as e:
        logger.warning(LogTarget.FILE, f"优化表创建失败，使用标准方法: {e}")
        # 保留索引
        return pa.Table.from_pandas(df, preserve_index=preserve_index)


# ParquetStorage类实现
class ParquetStorage:
    """
    优化版的Parquet存储类，提供高性能的数据存储和读取功能
    """
    
    def __init__(self, base_dir: str = "data"):
        """
        初始化存储对象
        
        Args:
            base_dir: 数据存储的基础目录
        """
        self.base_dir = base_dir
        logger.debug(LogTarget.FILE, f"初始化ParquetStorage，基础目录: {base_dir}")
        
    def save_data_by_partition(self, dataframe, symbol, period, partition_columns=None, append=False):
        """
        按分区保存数据
        
        Args:
            dataframe (pd.DataFrame): 要保存的数据
            symbol (str): 股票代码
            period (str): 数据周期
            partition_columns (list): 分区列名列表
            append (bool): 是否追加模式
            
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        if dataframe is None or dataframe.empty:
            logger.warning(LogTarget.FILE, "数据为空，无法保存")
            return False
            
        try:
            # 使用全局函数处理数据保存
            results = save_data_by_partition(
                dataframe,
                self.base_dir,
                symbol,
                period,
                engine='pyarrow',
                compression='snappy'
            )
            
            # 检查是否保存成功
            return len(results) > 0
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"保存数据时出错: {e}")
            return False
            
    def load_data(self, symbol, period):
        """
        加载指定股票和周期的数据
        
        Args:
            symbol (str): 股票代码
            period (str): 数据周期
            
        Returns:
            pd.DataFrame: 加载的数据，如果失败返回None
        """
        try:
            # 获取最新分区文件
            latest_file = get_latest_partition_file(self.base_dir, symbol, period)
            
            if not latest_file or not os.path.exists(latest_file):
                logger.warning(LogTarget.FILE, f"未找到数据文件: {symbol} {period}")
                return None
                
            # 读取数据
            df = pd.read_parquet(latest_file)
            logger.info(LogTarget.FILE, f"成功加载数据: {symbol} {period} ({len(df)} 行)")
            return df
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"加载数据时出错: {e}")
            return None
            
    def append_to_partition(self, dataframe, symbol, period, partition_columns=None):
        """
        将数据追加到现有分区
        
        Args:
            dataframe (pd.DataFrame): 要追加的数据
            symbol (str): 股票代码
            period (str): 数据周期
            partition_columns (list): 分区列名列表
            
        Returns:
            bool: 追加成功返回True，否则返回False
        """
        # 直接调用save_data_by_partition，并设置append=True
        return self.save_data_by_partition(dataframe, symbol, period, partition_columns, append=True)
        
    def save_data_by_partition_parallel(self, dataframe, symbol, period,
                                      partition_columns=None, num_workers=4,
                                      chunk_size=None):
        """
        并行保存数据 - 直接存储，不进行额外过滤

        注意：此函数不再进行尾部数据过滤，数据质量控制在上游处理：
        - 新合成数据：在period_handler中过滤首条
        - 增量更新：在incremental_update中过滤现有数据尾部

        Args:
            dataframe (pd.DataFrame): 要保存的数据（已经过质量控制）
            symbol (str): 股票代码
            period (str): 数据周期
            partition_columns (list): 分区列名列表
            num_workers (int): 并行工作进程数
            chunk_size (int): 每个分片的大小

        Returns:
            bool: 保存成功返回True，否则返回False
        """
        # 记录并行处理的参数
        logger.info(LogTarget.FILE, f"启用并行处理，工作进程数: {num_workers}")
        
        # 如果使用全局进程池，确保它处于活动状态
        if _use_global_pool:
            try:
                # 检查并获取全局进程池
                from utils.multiprocessing.global_process_pool import GlobalProcessPool
                pool = GlobalProcessPool.get_instance()
                
                # 检查进程池是否可用
                if not pool.is_active():
                    logger.info(LogTarget.FILE, "全局进程池不可用，将重新初始化")
                    GlobalProcessPool.reset()  # 重置单例状态
                    pool = GlobalProcessPool.get_instance()  # 获取新的实例
            except Exception as e:
                logger.warning(LogTarget.FILE, f"检查全局进程池状态时出错: {e}")
        
        # 使用统一的save_data_by_partition函数，设置parallel=True
        results = save_data_by_partition(
            dataframe,
            self.base_dir,
            symbol,
            period,
            engine='pyarrow',
            compression='snappy',
            parallel=True,
            max_workers=num_workers
        )
        
        # 检查是否保存成功
        success = len(results) > 0
        logger.info(LogTarget.FILE, f"并行处理完成，成功保存: {success}")
        return success
        
    def incremental_update(self, dataframe, symbol, period, partition_columns=None):
        """
        增量更新数据 - 正确的边界数据处理流程

        处理流程：
        1. 新合成数据：保持完整（已在period_handler中过滤首条）
        2. 现有数据：过滤尾部不完整数据
        3. 智能合并：处理重叠，确保数据连续性
        4. 直接存储：保留完整的合并数据

        Args:
            dataframe (pd.DataFrame): 新合成的数据（已过滤首条，保持完整）
            symbol (str): 股票代码
            period (str): 数据周期
            partition_columns (list): 分区列名列表

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        if dataframe is None or dataframe.empty:
            logger.warning(LogTarget.FILE, f"{symbol} 增量更新数据为空")
            return False

        try:
            # 获取新数据的时间范围
            from utils.data_processor.data_merger import get_data_time_range
            new_start, new_end = get_data_time_range(dataframe)

            if new_start is None or new_end is None:
                logger.error(LogTarget.FILE, f"{symbol} 无法获取新数据的时间范围")
                return False

            logger.info(LogTarget.FILE, f"{symbol} 增量更新数据时间范围: {new_start.strftime('%Y%m%d %H:%M:%S')} 至 {new_end.strftime('%Y%m%d %H:%M:%S')}")

            # 加载现有数据
            from data.storage.parquet_reader import read_partitioned_data
            existing_data = read_partitioned_data(
                data_root=self.base_dir,
                symbol=symbol,
                period=period
            )

            if existing_data is None or existing_data.empty:
                # 如果没有现有数据，直接保存新数据
                logger.info(LogTarget.FILE, f"{symbol} 无现有数据，直接保存新数据")
                return self.save_data_by_partition_parallel(dataframe, symbol, period, partition_columns)

            # 过滤现有数据的尾部不完整数据
            if len(existing_data) > 1:
                original_count = len(existing_data)
                existing_data = existing_data.iloc[:-1]  # 删除最后一条可能不完整的数据
                logger.info(LogTarget.FILE, f"{symbol} 现有数据过滤尾部不完整数据，从 {original_count} 行减少到 {len(existing_data)} 行")
            elif len(existing_data) == 1:
                logger.warning(LogTarget.FILE, f"{symbol} 现有数据只有 1 行，无法过滤尾部数据")

            # 获取现有数据的时间范围
            existing_start, existing_end = get_data_time_range(existing_data)
            logger.info(LogTarget.FILE, f"{symbol} 现有数据时间范围: {existing_start.strftime('%Y%m%d %H:%M:%S')} 至 {existing_end.strftime('%Y%m%d %H:%M:%S')}")

            # 使用智能数据合并
            from utils.data_processor.data_merger import merge_dataframes_smart

            merged_data = merge_dataframes_smart(
                old_data=existing_data,
                new_data=dataframe,
                symbol=symbol,
                period=period
            )

            if merged_data is None or merged_data.empty:
                logger.error(LogTarget.FILE, f"{symbol} 数据合并失败")
                return False

            # 获取合并后数据的时间范围
            merged_start, merged_end = get_data_time_range(merged_data)
            logger.info(LogTarget.FILE, f"{symbol} 合并后数据时间范围: {merged_start.strftime('%Y%m%d %H:%M:%S')} 至 {merged_end.strftime('%Y%m%d %H:%M:%S')}")
            logger.info(LogTarget.FILE, f"{symbol} 增量更新: 现有{len(existing_data)}行 + 新增{len(dataframe)}行 = 合并{len(merged_data)}行")
            logger.info(LogTarget.FILE, f"{symbol} 合并数据：\n{merged_data}\n")

            # 保存合并后的数据
            success = self.save_data_by_partition_parallel(merged_data, symbol, period, partition_columns)

            if success:
                logger.info(LogTarget.FILE, f"{symbol} 合并数据保存完成")
            else:
                logger.error(LogTarget.FILE, f"{symbol} 合并数据保存失败")

            return success

        except Exception as e:
            logger.error(LogTarget.FILE, f"{symbol} 增量更新失败: {e}", exc_info=True)
            return False