#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能通用时间转换器

专为金融数据设计的时间转换器，完全替代pd.to_datetime。
解决pd.to_datetime在金融数据处理中的时区偏移问题。

核心特性：
1. 智能类型检测：自动识别毫秒时间戳、秒时间戳、字符串时间
2. 时区安全：避免pd.to_datetime的8小时偏移问题
3. 高性能转换：保持130倍性能优势
4. 严格验证：对无法识别的格式抛出错误，而非使用有风险的后备方案
5. 统一管理：修改一处影响全项目

设计理念：
- 准确性优于兼容性：宁可报错也不使用可能有时区问题的转换
- 性能优于功能：选择最快的转换路径
- 明确优于模糊：要求明确的时间格式，避免自动推断的风险
- 统一优于分散：集中管理所有时间转换逻辑

⚠️ 重要说明：
本模块不使用pd.to_datetime作为后备方案，因为pd.to_datetime在处理金融数据时
经常导致时区问题（如8小时偏移）。如果遇到无法识别的时间格式，请：
1. 使用明确的时间戳格式（毫秒或秒）
2. 指定明确的format参数
3. 预处理数据为标准格式
"""

import datetime
import pandas as pd
import numpy as np
import warnings
from typing import Union, Optional, Any, List, Dict
from functools import lru_cache

# 导入高性能转换函数
from utils.time_utils import (
    fast_ms_to_datetime_index,
    s_to_datetime_index,
    simple_string_to_datetime_list,
    simple_ms_to_datetime,
    s_to_datetime
)


class TimeConversionError(Exception):
    """时间转换异常类"""
    pass


class SmartTimeConverter:
    """智能时间转换器类"""
    
    def __init__(self):
        self.stats = {
            'total_calls': 0,
            'ms_conversions': 0,
            's_conversions': 0,
            'string_conversions': 0,
            'fallback_conversions': 0,
            'errors': 0
        }
        
        # 常见时间格式（按使用频率排序）
        self.common_formats = [
            '%Y%m%d%H%M%S',      # 20250118080000
            '%Y%m%d',            # 20250118
            '%Y-%m-%d %H:%M:%S', # 2025-01-18 08:00:00
            '%Y-%m-%d',          # 2025-01-18
            '%Y/%m/%d %H:%M:%S', # 2025/01/18 08:00:00
            '%Y/%m/%d',          # 2025/01/18
            '%Y%m%d%H%M',        # 202501180800
            '%m/%d/%Y',          # 01/18/2025
            '%d/%m/%Y',          # 18/01/2025
        ]
    
    @lru_cache(maxsize=128)
    def detect_input_type(self, sample_value: Any, unit: Optional[str] = None) -> str:
        """
        智能检测输入数据类型
        
        Args:
            sample_value: 样本值
            unit: 显式指定的单位
            
        Returns:
            检测到的类型：'ms', 's', 'string', 'unknown'
        """
        # 显式参数优先
        if unit in ['ms', 's']:
            return unit
        
        # 数值类型检测
        if isinstance(sample_value, (int, float, np.integer, np.floating)):
            return self._detect_timestamp_range(float(sample_value))
        
        # 字符串类型
        if isinstance(sample_value, (str, np.str_)):
            return 'string'
        
        return 'unknown'
    
    def _detect_timestamp_range(self, value: float) -> str:
        """
        根据数值范围判断时间戳类型

        Args:
            value: 数值

        Returns:
            时间戳类型：'ms', 's', 'unknown'
        """
        if value > 1e12:  # 大于1万亿，毫秒时间戳（约2001年后）
            return 'ms'
        elif value > 1e9:  # 大于10亿，秒时间戳（约2001年后）
            return 's'
        elif value >= 0:   # 包括0，都视为秒时间戳（1970年起）
            return 's'
        else:
            return 'unknown'
    
    @lru_cache(maxsize=64)
    def auto_detect_format(self, sample_str: str) -> Optional[str]:
        """
        自动检测字符串时间格式
        
        Args:
            sample_str: 样本字符串
            
        Returns:
            检测到的格式，None表示无法识别
        """
        sample_str = str(sample_str).strip()
        
        for fmt in self.common_formats:
            try:
                datetime.datetime.strptime(sample_str, fmt)
                return fmt
            except ValueError:
                continue
        
        return None
    
    def _get_sample_value(self, data: Any) -> Any:
        """
        从数据中获取样本值用于类型检测
        
        Args:
            data: 输入数据
            
        Returns:
            样本值
        """
        if hasattr(data, '__iter__') and not isinstance(data, str):
            # 序列数据，取第一个非空值
            for item in data:
                if item is not None and not pd.isna(item):
                    return item
            return None
        else:
            # 单个值
            return data
    
    def _convert_single_value(self, value: Any, conversion_type: str, 
                            format_str: Optional[str] = None) -> datetime.datetime:
        """
        转换单个值
        
        Args:
            value: 要转换的值
            conversion_type: 转换类型
            format_str: 字符串格式
            
        Returns:
            转换后的datetime对象
        """
        if conversion_type == 'ms':
            return simple_ms_to_datetime(value)
        elif conversion_type == 's':
            return s_to_datetime(value)
        elif conversion_type == 'string':
            if format_str:
                return datetime.datetime.strptime(str(value), format_str)
            else:
                # 尝试自动检测格式
                detected_format = self.auto_detect_format(str(value))
                if detected_format:
                    return datetime.datetime.strptime(str(value), detected_format)
                else:
                    # 无法识别格式，抛出错误而不是使用可能有时区问题的pd.to_datetime
                    raise TimeConversionError(f"无法识别时间格式 '{value}'，请使用明确的时间戳格式或指定format参数")
        else:
            raise TimeConversionError(f"不支持的转换类型: {conversion_type}")
    
    def _convert_batch_values(self, data: Any, conversion_type: str,
                            format_str: Optional[str] = None) -> pd.DatetimeIndex:
        """
        批量转换值
        
        Args:
            data: 要转换的数据
            conversion_type: 转换类型
            format_str: 字符串格式
            
        Returns:
            转换后的DatetimeIndex
        """
        if conversion_type == 'ms':
            return fast_ms_to_datetime_index(data)
        elif conversion_type == 's':
            return s_to_datetime_index(data)
        elif conversion_type == 'string':
            if format_str:
                return simple_string_to_datetime_list(data, format_str)
            else:
                # 尝试自动检测格式
                sample = self._get_sample_value(data)
                if sample is not None:
                    detected_format = self.auto_detect_format(str(sample))
                    if detected_format:
                        return simple_string_to_datetime_list(data, detected_format)
                
                # 无法识别格式，抛出错误而不是使用可能有时区问题的pd.to_datetime
                if hasattr(data, '__len__'):
                    raise TimeConversionError(f"无法识别时间格式，请使用明确的时间戳格式或指定format参数。数据样本: {data[:3] if len(data) > 3 else data}")
                else:
                    raise TimeConversionError(f"无法识别时间格式 '{data}'，请使用明确的时间戳格式或指定format参数")
        else:
            raise TimeConversionError(f"不支持的转换类型: {conversion_type}")


# 全局转换器实例
_converter = SmartTimeConverter()


def smart_to_datetime(
    data: Union[int, float, str, List, pd.Series, np.ndarray],
    unit: Optional[str] = None,
    format: Optional[str] = None,
    errors: str = 'raise',
    dayfirst: bool = False,
    yearfirst: bool = False,
    utc: Optional[bool] = None,
    infer_datetime_format: bool = False,
    origin: str = 'unix',
    cache: bool = True,
    **kwargs
) -> Union[datetime.datetime, pd.DatetimeIndex]:
    """
    智能时间转换器 - 专为金融数据设计，完全替代pd.to_datetime

    自动检测输入类型并选择最优转换方法：
    - 毫秒时间戳 → fast_ms_to_datetime_index() (130倍性能)
    - 秒时间戳 → s_to_datetime_index()
    - 字符串时间 → simple_string_to_datetime_list()
    - 严格验证 → 对无法识别的格式抛出错误

    Args:
        data: 输入数据（时间戳、字符串、Series等）
        unit: 时间戳单位（'ms', 's'），None为自动检测
        format: 字符串格式，None为自动检测（建议明确指定）
        errors: 错误处理方式（'raise', 'coerce', 'ignore'）
        dayfirst: 日期优先解析（保持兼容性，暂不实现）
        yearfirst: 年份优先解析（保持兼容性，暂不实现）
        utc: UTC时区处理（保持兼容性，暂不实现）
        infer_datetime_format: 推断格式（保持兼容性）
        origin: 时间戳起点（保持兼容性）
        cache: 缓存结果（保持兼容性）
        **kwargs: 其他兼容性参数

    Returns:
        datetime对象或DatetimeIndex

    Raises:
        TimeConversionError: 当无法识别时间格式时抛出，避免使用可能有时区问题的pd.to_datetime

    Note:
        ⚠️ 重要：本函数不使用pd.to_datetime作为后备方案，因为pd.to_datetime在处理
        金融数据时经常导致时区问题（如8小时偏移）。如果遇到无法识别的格式，
        请使用明确的时间戳格式或指定format参数。

    Examples:
        >>> # 毫秒时间戳（自动检测）
        >>> smart_to_datetime([1737158400000, 1737158460000])

        >>> # 显式指定毫秒时间戳
        >>> smart_to_datetime([1737158400000, 1737158460000], unit='ms')

        >>> # 秒时间戳
        >>> smart_to_datetime([1737158400, 1737158460], unit='s')

        >>> # 字符串时间（自动检测格式）
        >>> smart_to_datetime(['20250118080000', '20250118080100'])

        >>> # 字符串时间（指定格式）
        >>> smart_to_datetime(['2025-01-18', '2025-01-19'], format='%Y-%m-%d')

        >>> # 单个值转换
        >>> smart_to_datetime('2025-01-18 08:00:00')

        >>> # 错误处理
        >>> smart_to_datetime(['invalid'], errors='coerce')  # 返回NaT
    """
    import time
    start_time = time.time()

    try:
        _converter.stats['total_calls'] += 1

        # 处理空数据
        if data is None:
            return pd.DatetimeIndex([])

        # 处理空序列
        if hasattr(data, '__len__') and len(data) == 0:
            return pd.DatetimeIndex([])

        # 获取样本值进行类型检测
        sample_value = _converter._get_sample_value(data)
        if sample_value is None or pd.isna(sample_value):
            if hasattr(data, '__iter__') and not isinstance(data, str):
                return pd.DatetimeIndex([pd.NaT] * len(data))
            else:
                return pd.NaT

        # 智能检测输入类型
        conversion_type = _converter.detect_input_type(sample_value, unit)

        # 处理未知类型
        if conversion_type == 'unknown':
            if errors == 'raise':
                raise TimeConversionError(f"无法识别的时间格式: {sample_value}")
            elif errors == 'coerce':
                if hasattr(data, '__iter__') and not isinstance(data, str):
                    return pd.DatetimeIndex([pd.NaT] * len(data))
                else:
                    return pd.NaT
            else:  # errors == 'ignore'
                return data

        # 更新统计
        if conversion_type == 'ms':
            _converter.stats['ms_conversions'] += 1
        elif conversion_type == 's':
            _converter.stats['s_conversions'] += 1
        elif conversion_type == 'string':
            _converter.stats['string_conversions'] += 1

        # 判断是单个值还是批量值
        is_scalar = not hasattr(data, '__iter__') or isinstance(data, str)

        if is_scalar:
            # 单个值转换
            result = _converter._convert_single_value(data, conversion_type, format)
            return result
        else:
            # 批量转换
            result = _converter._convert_batch_values(data, conversion_type, format)
            return result
            
    except Exception as e:
        _converter.stats['errors'] += 1

        # 记录性能（失败情况）
        duration = time.time() - start_time
        try:
            from utils.time_converter_config import get_manager
            manager = get_manager()
            data_size = len(data) if hasattr(data, '__len__') and not isinstance(data, str) else 1
            manager.record_conversion('error', duration, False, data_size)
        except:
            pass  # 忽略监控错误

        if errors == 'raise':
            raise TimeConversionError(f"时间转换失败: {e}")
        elif errors == 'coerce':
            # 返回NaT
            if hasattr(data, '__iter__') and not isinstance(data, str):
                return pd.DatetimeIndex([pd.NaT] * len(data))
            else:
                return pd.NaT
        elif errors == 'ignore':
            # 返回原始数据
            return data
        else:
            raise ValueError(f"不支持的errors参数: {errors}")

    finally:
        # 记录性能（成功情况）
        if '_converter' in locals() and 'start_time' in locals():
            duration = time.time() - start_time
            try:
                from utils.time_converter_config import get_manager
                manager = get_manager()
                data_size = len(data) if hasattr(data, '__len__') and not isinstance(data, str) else 1
                conversion_type = locals().get('conversion_type', 'unknown')
                manager.record_conversion(conversion_type, duration, True, data_size)
            except:
                pass  # 忽略监控错误


def get_conversion_stats() -> Dict:
    """
    获取转换统计信息
    
    Returns:
        统计信息字典
    """
    return _converter.stats.copy()


def reset_conversion_stats():
    """重置转换统计信息"""
    _converter.stats = {
        'total_calls': 0,
        'ms_conversions': 0,
        's_conversions': 0,
        'string_conversions': 0,
        'fallback_conversions': 0,
        'errors': 0
    }


# 向后兼容的别名
pd_to_datetime_replacement = smart_to_datetime
