#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能时间转换器配置和监控模块

提供全局配置管理和性能监控功能，实现统一管理。
"""

import time
import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class TimeConverterConfig:
    """时间转换器配置类"""
    
    # 基本配置
    default_timezone: str = 'Asia/Shanghai'
    cache_size: int = 128
    enable_monitoring: bool = True
    enable_warnings: bool = True
    
    # 性能配置
    batch_threshold: int = 100  # 批量处理阈值
    max_cache_age: int = 3600   # 缓存最大年龄（秒）
    
    # 兼容性配置
    fallback_to_pandas: bool = True
    strict_mode: bool = False
    
    # 调试配置
    debug_mode: bool = False
    log_conversions: bool = False
    
    def update(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"未知配置项: {key}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)


class TimeConverterMonitor:
    """时间转换器性能监控类"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._stats = {
            'total_calls': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'error_count': 0,
            'error_rate': 0.0,
            'type_distribution': {
                'ms': 0,
                's': 0,
                'string': 0,
                'fallback': 0
            },
            'performance_history': [],
            'last_reset': datetime.now()
        }
    
    def record_conversion(self, conversion_type: str, duration: float, 
                         success: bool = True, data_size: int = 1):
        """
        记录转换统计
        
        Args:
            conversion_type: 转换类型
            duration: 转换耗时（秒）
            success: 是否成功
            data_size: 数据大小
        """
        with self._lock:
            self._stats['total_calls'] += 1
            self._stats['total_time'] += duration
            self._stats['avg_time'] = self._stats['total_time'] / self._stats['total_calls']
            
            # 更新最小/最大时间
            if duration < self._stats['min_time']:
                self._stats['min_time'] = duration
            if duration > self._stats['max_time']:
                self._stats['max_time'] = duration
            
            # 错误统计
            if not success:
                self._stats['error_count'] += 1
            self._stats['error_rate'] = self._stats['error_count'] / self._stats['total_calls']
            
            # 类型分布
            if conversion_type in self._stats['type_distribution']:
                self._stats['type_distribution'][conversion_type] += 1
            
            # 性能历史（保留最近100条记录）
            self._stats['performance_history'].append({
                'timestamp': datetime.now(),
                'type': conversion_type,
                'duration': duration,
                'data_size': data_size,
                'success': success
            })
            
            # 限制历史记录数量
            if len(self._stats['performance_history']) > 100:
                self._stats['performance_history'] = self._stats['performance_history'][-100:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                'summary': {
                    'total_conversions': self._stats['total_calls'],
                    'average_time_ms': self._stats['avg_time'] * 1000,
                    'min_time_ms': self._stats['min_time'] * 1000 if self._stats['min_time'] != float('inf') else 0,
                    'max_time_ms': self._stats['max_time'] * 1000,
                    'error_rate_percent': self._stats['error_rate'] * 100,
                    'total_errors': self._stats['error_count']
                },
                'type_distribution': self._stats['type_distribution'].copy(),
                'performance_trend': self._get_performance_trend(),
                'last_reset': self._stats['last_reset']
            }
    
    def _get_performance_trend(self) -> Dict[str, float]:
        """获取性能趋势"""
        if len(self._stats['performance_history']) < 10:
            return {'trend': 'insufficient_data'}
        
        recent_10 = self._stats['performance_history'][-10:]
        recent_avg = sum(record['duration'] for record in recent_10) / len(recent_10)
        
        if len(self._stats['performance_history']) >= 20:
            previous_10 = self._stats['performance_history'][-20:-10]
            previous_avg = sum(record['duration'] for record in previous_10) / len(previous_10)
            
            if previous_avg > 0:
                change_percent = ((recent_avg - previous_avg) / previous_avg) * 100
                return {
                    'recent_avg_ms': recent_avg * 1000,
                    'previous_avg_ms': previous_avg * 1000,
                    'change_percent': change_percent,
                    'trend': 'improving' if change_percent < -5 else 'degrading' if change_percent > 5 else 'stable'
                }
        
        return {
            'recent_avg_ms': recent_avg * 1000,
            'trend': 'stable'
        }
    
    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self._stats = {
                'total_calls': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'min_time': float('inf'),
                'max_time': 0.0,
                'error_count': 0,
                'error_rate': 0.0,
                'type_distribution': {
                    'ms': 0,
                    's': 0,
                    'string': 0,
                    'fallback': 0
                },
                'performance_history': [],
                'last_reset': datetime.now()
            }
    
    def get_performance_report(self) -> str:
        """生成性能报告"""
        stats = self.get_stats()
        
        report = f"""
智能时间转换器性能报告
{'='*50}

总体统计:
  总转换次数: {stats['summary']['total_conversions']:,}
  平均耗时: {stats['summary']['average_time_ms']:.3f} ms
  最小耗时: {stats['summary']['min_time_ms']:.3f} ms
  最大耗时: {stats['summary']['max_time_ms']:.3f} ms
  错误率: {stats['summary']['error_rate_percent']:.2f}%
  总错误数: {stats['summary']['total_errors']}

类型分布:
  毫秒时间戳: {stats['type_distribution']['ms']:,} 次
  秒时间戳: {stats['type_distribution']['s']:,} 次
  字符串时间: {stats['type_distribution']['string']:,} 次
  后备转换: {stats['type_distribution']['fallback']:,} 次

性能趋势:
  最近平均耗时: {stats['performance_trend'].get('recent_avg_ms', 0):.3f} ms
  趋势: {stats['performance_trend'].get('trend', 'unknown')}

最后重置时间: {stats['last_reset']}
"""
        return report


class TimeConverterManager:
    """时间转换器管理器"""
    
    def __init__(self):
        self.config = TimeConverterConfig()
        self.monitor = TimeConverterMonitor()
        self._enabled = True
    
    def update_config(self, **kwargs):
        """更新配置"""
        self.config.update(**kwargs)
    
    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return self.config.to_dict()
    
    def enable_monitoring(self):
        """启用监控"""
        self._enabled = True
        self.config.enable_monitoring = True
    
    def disable_monitoring(self):
        """禁用监控"""
        self._enabled = False
        self.config.enable_monitoring = False
    
    def record_conversion(self, conversion_type: str, duration: float, 
                         success: bool = True, data_size: int = 1):
        """记录转换（如果启用监控）"""
        if self._enabled and self.config.enable_monitoring:
            self.monitor.record_conversion(conversion_type, duration, success, data_size)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.monitor.get_stats()
    
    def get_performance_report(self) -> str:
        """获取性能报告"""
        return self.monitor.get_performance_report()
    
    def reset_stats(self):
        """重置统计"""
        self.monitor.reset_stats()
    
    def is_monitoring_enabled(self) -> bool:
        """检查监控是否启用"""
        return self._enabled and self.config.enable_monitoring


# 全局管理器实例
_manager = TimeConverterManager()


def get_manager() -> TimeConverterManager:
    """获取全局管理器实例"""
    return _manager


def update_global_config(**kwargs):
    """更新全局配置"""
    _manager.update_config(**kwargs)


def get_global_config() -> Dict[str, Any]:
    """获取全局配置"""
    return _manager.get_config()


def get_performance_stats() -> Dict[str, Any]:
    """获取性能统计"""
    return _manager.get_performance_stats()


def get_performance_report() -> str:
    """获取性能报告"""
    return _manager.get_performance_report()


def reset_performance_stats():
    """重置性能统计"""
    _manager.reset_stats()


# 性能监控装饰器
def monitor_conversion(conversion_type: str):
    """
    性能监控装饰器
    
    Args:
        conversion_type: 转换类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not _manager.is_monitoring_enabled():
                return func(*args, **kwargs)
            
            start_time = time.time()
            success = True
            data_size = 1
            
            try:
                # 尝试估算数据大小
                if args:
                    data = args[0]
                    if hasattr(data, '__len__') and not isinstance(data, str):
                        data_size = len(data)
                
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                _manager.record_conversion(conversion_type, duration, success, data_size)
        
        return wrapper
    return decorator
