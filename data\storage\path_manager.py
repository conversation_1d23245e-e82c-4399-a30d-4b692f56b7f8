#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
路径管理模块

提供数据文件路径管理功能，包括：
1. 获取数据根目录
2. 获取分区文件列表
3. 获取最新数据文件
"""

import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime

import pandas as pd

from utils.logger import get_unified_logger, LogTarget
from utils.smart_time_converter import smart_to_datetime

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


def get_data_root(data_root: Optional[str] = None) -> str:
    """
    获取数据根目录
    
    Args:
        data_root: 指定的数据根目录，如果为None则使用环境变量或默认目录
        
    Returns:
        str: 数据根目录路径
    """
    if data_root:
        return data_root
    
    # 从环境变量获取
    if "DATA_ROOT" in os.environ:
        return os.environ["DATA_ROOT"]
    
    # 默认路径
    return os.path.join(os.path.expanduser("~"), "data")


def get_partition_files(data_root: str, market: str, code: str, period: str, 
                      start_time: Optional[str] = None, 
                      end_time: Optional[str] = None,
                      limit: Optional[int] = None) -> List[str]:
    """
    获取指定时间范围内的分区文件列表
    
    Args:
        data_root: 数据根目录
        market: 市场代码
        code: 股票代码
        period: 数据周期
        start_time: 开始时间 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")，如果为None则不限制开始时间
        end_time: 结束时间 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")，如果为None则不限制结束时间
        limit: 限制返回的文件数量，如果为None则不限制。当limit为正数时，返回前limit个文件；
              当limit为负数时，返回后|limit|个文件；当limit为(head, tail)元组时，返回前head个和后tail个文件
        
    Returns:
        List[str]: 分区文件路径列表
    """
    try:
        base_dir = os.path.join(data_root, market, code, period)
        logger.debug(LogTarget.FILE, f"基础目录: {base_dir}")
        
        if not os.path.exists(base_dir):
            logger.debug(LogTarget.FILE, f"基础目录不存在: {base_dir}")
            return []
        
        # 解析时间范围
        start_dt = None
        end_dt = None
        
        if start_time:
            try:
                if len(start_time) == 8:  # YYYYMMDD
                    start_dt = datetime.strptime(start_time, '%Y%m%d')
                elif len(start_time) == 14:  # YYYYMMDDHHMMSS
                    start_dt = datetime.strptime(start_time, '%Y%m%d%H%M%S')
                else:
                    # 不支持的格式，明确报错
                    raise ValueError(f"不支持的开始时间格式: {start_time}。支持格式: YYYYMMDD、YYYYMMDDHHMMSS")
                logger.debug(LogTarget.FILE, f"解析开始时间: {start_time} -> {start_dt}")
            except ValueError:
                logger.warning(LogTarget.FILE, f"无法解析开始时间 {start_time}")
        
        if end_time:
            try:
                if len(end_time) == 8:  # YYYYMMDD
                    end_dt = datetime.strptime(end_time, '%Y%m%d')
                elif len(end_time) == 14:  # YYYYMMDDHHMMSS
                    end_dt = datetime.strptime(end_time, '%Y%m%d%H%M%S')
                else:
                    # 不支持的格式，明确报错
                    raise ValueError(f"不支持的结束时间格式: {end_time}。支持格式: YYYYMMDD、YYYYMMDDHHMMSS")
                logger.debug(LogTarget.FILE, f"解析结束时间: {end_time} -> {end_dt}")
            except ValueError:
                logger.warning(LogTarget.FILE, f"无法解析结束时间 {end_time}")
        
        result_files = []
        
        try:
            # 直接根据周期类型检查分区目录
            if period.lower() == 'tick':
                # tick数据按日分区
                for year_dir in sorted(os.listdir(base_dir)):
                    if not year_dir.isdigit():
                        continue
                    
                    year = int(year_dir)
                    
                    # 跳过不在时间范围内的年份
                    if (start_dt and year < start_dt.year) or (end_dt and year > end_dt.year):
                        continue
                    
                    year_path = os.path.join(base_dir, year_dir)
                    for month_dir in sorted(os.listdir(year_path)):
                        if not month_dir.isdigit():
                            continue
                        
                        month = int(month_dir)
                        
                        # 跳过不在时间范围内的月份
                        if ((start_dt and (year < start_dt.year or 
                                          (year == start_dt.year and month < start_dt.month))) or 
                            (end_dt and (year > end_dt.year or 
                                        (year == end_dt.year and month > end_dt.month)))):
                            continue
                        
                        month_path = os.path.join(year_path, month_dir)
                        for day_file in sorted(os.listdir(month_path)):
                            if not day_file.endswith('.parquet'):
                                continue
                            
                            day = int(day_file.split('.')[0])
                            
                            # 跳过不在时间范围内的日期
                            date_check = ((start_dt and (datetime(year, month, day) < 
                                          start_dt.replace(hour=0, minute=0, second=0))) or 
                                         (end_dt and (datetime(year, month, day) > 
                                         end_dt.replace(hour=23, minute=59, second=59))))
                            if date_check:
                                continue
                            
                            result_files.append(os.path.join(month_path, day_file))
            else:
                # 其他周期按年分区
                for year_file in sorted(os.listdir(base_dir)):
                    if not year_file.endswith('.parquet'):
                        continue
                    
                    year = int(year_file.split('.')[0])
                    
                    # 跳过不在时间范围内的年份
                    if (start_dt and year < start_dt.year) or (end_dt and year > end_dt.year):
                        continue
                    
                    result_files.append(os.path.join(base_dir, year_file))
        except Exception as e:
            logger.error(LogTarget.FILE, f"获取分区文件列表失败: {e}")
        
        # 处理limit参数
        if result_files and limit is not None:
            sorted_files = sorted(result_files)
            if isinstance(limit, tuple) and len(limit) == 2:
                head_limit, tail_limit = limit
                head_files = sorted_files[:head_limit] if head_limit > 0 else []
                tail_files = sorted_files[-tail_limit:] if tail_limit > 0 else []
                logger.debug(LogTarget.FILE, f"应用limit参数 (head={head_limit}, tail={tail_limit})")
                return head_files + tail_files
            elif isinstance(limit, int):
                if limit > 0:
                    logger.debug(LogTarget.FILE, f"应用正limit参数: {limit}")
                    return sorted_files[:limit]
                else:
                    logger.debug(LogTarget.FILE, f"应用负limit参数: {limit}")
                    return sorted_files[limit:]  # 负数索引会从末尾开始计数
        
        logger.debug(LogTarget.FILE, f"找到 {len(result_files)} 个分区文件")
        return sorted(result_files)
    except Exception as e:
        logger.error(LogTarget.FILE, f"获取分区文件列表时出错: {e}")
        return []

def parse_symbol(symbol: str) -> Tuple[str, str]:
    """
    解析股票代码，获取市场和代码

    Args:
        symbol: 股票代码 (例如: "000001.SZ" 或 "600000.SH")

    Returns:
        Tuple[str, str]: (代码, 市场) 元组
        
    Raises:
        ValueError: 如果股票代码格式不正确
    """
    if not isinstance(symbol, str) or '.' not in symbol:
        msg = (f"无效的股票代码格式: '{symbol}'. "
               f"期望格式为 'code.market' (例如 '000001.SZ')")
        logger.error(msg)
        raise ValueError(msg)
        
    parts = symbol.split('.')
    if len(parts) != 2 or not parts[0] or not parts[1]:
        msg = (f"无效的股票代码格式: '{symbol}'. "
               f"期望格式为 'code.market' (例如 '000001.SZ')")
        logger.error(msg)
        raise ValueError(msg)
        
    code = parts[0]
    market = parts[1].upper()  # 统一使用大写市场代码
    
    return code, market

def get_partitioned_path(data_root: str, symbol: str, period: str, timestamp: Optional[str] = None) -> str:
    """
    根据数据根目录、股票代码、周期和时间戳生成分区存储路径

    分区规则:
    - tick数据: <data_root>/<市场>/<代码>/tick/<年>/<月>/<日>.parquet
    - 其他周期: <data_root>/<市场>/<代码>/<周期>/<年>.parquet

    Args:
        data_root: 数据根目录
        symbol: 股票代码 (例如: "000001.SZ" 或 "600000.SH")
        period: 数据周期 (例如: "1d", "1m", "tick")
        timestamp: 时间戳，支持以下格式：
                  - 数值时间戳 (int/float): 毫秒或秒级时间戳
                  - "YYYYMMDD": 8位日期字符串
                  - "YYYYMMDDHHMMSS": 14位时间字符串
                  - None: 使用当前日期

    Returns:
        str: 分区存储路径

    Raises:
        ValueError: 当timestamp格式不支持时
    """
    try:
        code, market = parse_symbol(symbol)
        
        # 解析时间戳 - 使用标准格式
        if timestamp:
            try:
                if isinstance(timestamp, (int, float)):
                    # 数值时间戳，明确指定单位
                    dt = smart_to_datetime(timestamp, unit='ms' if timestamp > 1e10 else 's')
                elif isinstance(timestamp, str) and len(timestamp) == 8:  # YYYYMMDD
                    dt = datetime.strptime(timestamp, '%Y%m%d')
                elif isinstance(timestamp, str) and len(timestamp) == 14:  # YYYYMMDDHHMMSS
                    dt = datetime.strptime(timestamp, '%Y%m%d%H%M%S')
                else:
                    # 不支持的格式，明确报错
                    raise ValueError(f"不支持的时间戳格式: {timestamp} (类型: {type(timestamp)})。支持格式: 数值时间戳、YYYYMMDD、YYYYMMDDHHMMSS")
            except ValueError:
                logger.warning(f"无法解析时间戳 {timestamp}，使用当前日期")
                dt = datetime.now()
        else:
            # 使用当前时间
            dt = datetime.now()
        
        year, month, day = dt.year, dt.month, dt.day
        
        # 根据周期类型生成不同的路径
        if period.lower() == 'tick':
            # tick数据按日分区
            result_path = os.path.join(data_root, market, code, period, 
                               f"{year}", f"{month:02d}", f"{day:02d}.parquet")
        else:
            # 其他周期按年分区
            result_path = os.path.join(data_root, market, code, period, f"{year}.parquet")
        
        return result_path
    except Exception as e:
        logger.error(f"生成分区路径时出错: {e}")
        raise

def get_base_dir(data_root: str, symbol: str, period: str) -> str:
    """
    获取股票数据的基础目录
    
    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        
    Returns:
        str: 基础目录路径
    """
    code, market = parse_symbol(symbol)
    return os.path.join(data_root, market, code, period)

def get_latest_partition_file(data_root: str, symbol: str, period: str) -> Optional[str]:
    """
    获取最新的分区文件路径
    
    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        
    Returns:
        Optional[str]: 最新分区文件路径，如果不存在则返回None
    """
    try:
        base_dir = get_base_dir(data_root, symbol, period)
        
        if not os.path.exists(base_dir):
            logger.debug(f"基础目录不存在: {base_dir}")
            return None
        
        try:
            logger.info(f"正在查找 {symbol} 的最新 {period} 数据文件，基础目录: {base_dir}")
            
            if period.lower() == 'tick':
                # 查找最新的年/月/日目录
                years = sorted([y for y in os.listdir(base_dir) if y.isdigit()], reverse=True)
                if not years:
                    logger.warning(f"未找到任何年份目录: {base_dir}")
                    return None
                
                logger.debug(f"找到年份目录: {years}")
                year_dir = os.path.join(base_dir, years[0])
                months = sorted([m for m in os.listdir(year_dir) if m.isdigit()], reverse=True)
                if not months:
                    logger.warning(f"未找到任何月份目录: {year_dir}")
                    return None
                
                logger.debug(f"找到月份目录: {months}")
                month_dir = os.path.join(year_dir, months[0])
                day_files = [d for d in os.listdir(month_dir) if d.endswith('.parquet')]
                if not day_files:
                    logger.warning(f"未找到任何日期文件: {month_dir}")
                    return None
                
                # 解析文件名并按日期排序
                day_files_sorted = sorted([d.split('.')[0] for d in day_files], reverse=True)
                logger.debug(f"找到日期文件: {day_files_sorted}")
                
                latest_file = os.path.join(month_dir, f"{day_files_sorted[0]}.parquet")
                logger.info(f"找到最新的tick数据文件: {latest_file}")
                return latest_file
            else:
                # 查找最新的年份文件
                year_files = [y for y in os.listdir(base_dir) if y.endswith('.parquet')]
                if not year_files:
                    logger.warning(f"未找到任何年份文件: {base_dir}")
                    return None
                
                # 解析文件名并按年份排序
                year_files_sorted = sorted([y.split('.')[0] for y in year_files], reverse=True)
                logger.debug(f"找到年份文件: {year_files_sorted}")
                
                latest_file = os.path.join(base_dir, f"{year_files_sorted[0]}.parquet")
                logger.info(f"找到最新的{period}数据文件: {latest_file}")
                return latest_file
        except Exception as e:
            logger.error(f"获取最新分区文件失败: {e}", exc_info=True)
            return None
    except Exception as e:
        logger.error(f"获取最新分区文件时出错: {e}")
        return None

def get_legacy_path(data_root: str, symbol: str, period: str) -> str:
    """
    获取旧版存储路径（向后兼容）
    
    旧版路径格式: <data_root>/<交易所>/<代码>/<周期>.parquet
    
    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        
    Returns:
        str: 旧版存储路径
    """
    code, market = parse_symbol(symbol)
    file_name = f"{period}.parquet"
    return os.path.join(data_root, market, code, file_name)

def get_save_path(data_root: str, symbol: str, period: str, timestamp: Optional[str] = None) -> str:
    """
    获取数据保存路径
    
    使用分区存储路径格式
    - tick数据: <data_root>/<市场>/<代码>/tick/<年>/<月>/<日>.parquet
    - 其他周期: <data_root>/<市场>/<代码>/<周期>/<年>.parquet
    
    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        timestamp: 时间戳 (格式: "YYYYMMDD" 或 "YYYYMMDDHHMMSS")，如果为None则使用当前日期
        
    Returns:
        str: 数据保存路径
    """
    # 已完全迁移到分区存储系统
    return get_partitioned_path(data_root, symbol, period, timestamp)

# 示例用法
if __name__ == '__main__':
    # 使用模块级别的日志记录器
    
    # 测试获取数据根目录
    root = get_data_root()
    print(f"默认数据根目录: {root}")
    
    # 测试获取分区路径
    symbol = "000001.SZ"
    print(f"Tick数据分区路径: {get_partitioned_path(root, symbol, 'tick')}")
    print(f"1分钟数据分区路径: {get_partitioned_path(root, symbol, '1m')}")
    print(f"日线数据分区路径: {get_partitioned_path(root, symbol, '1d')}")
    
    # 测试获取特定日期的分区路径
    print(f"特定日期的Tick数据路径: {get_partitioned_path(root, symbol, 'tick', '20250401')}")
    print(f"特定年份的分钟数据路径: {get_partitioned_path(root, symbol, '1m', '20250401')}") 