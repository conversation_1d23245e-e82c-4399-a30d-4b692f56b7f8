#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试智能配置生成功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.批量合成历史数据 import generate_synthesis_configs, get_optimal_source_period, generate_config_name

def test_config_generation():
    """测试配置生成功能"""
    print("🧪 测试智能配置生成功能")
    print("="*50)
    
    # 测试目标周期列表
    target_periods = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h']
    
    print(f"📋 测试目标周期: {target_periods}")
    print()
    
    # 生成配置
    configs = generate_synthesis_configs(target_periods)
    
    print(f"✅ 生成配置数量: {len(configs)}")
    print()
    
    # 显示每个配置的详细信息
    print("📊 配置详情:")
    for i, config in enumerate(configs, 1):
        source = config['source_period']
        target = config['target_period']
        name = config['config_name']
        print(f"   {i}. {source} → {target} ({name})")
    
    print()
    print("🎯 重点验证:")
    
    # 验证1m周期是否使用tick作为源
    config_1m = next((c for c in configs if c['target_period'] == '1m'), None)
    if config_1m:
        if config_1m['source_period'] == 'tick':
            print("   ✅ 1m周期正确使用tick作为源周期")
        else:
            print(f"   ❌ 1m周期源周期错误: {config_1m['source_period']}")
    
    # 验证其他周期是否使用1m作为源
    other_configs = [c for c in configs if c['target_period'] != '1m']
    all_use_1m = all(c['source_period'] == '1m' for c in other_configs)
    if all_use_1m:
        print("   ✅ 其他周期正确使用1m作为源周期")
    else:
        print("   ❌ 部分周期源周期不是1m")
        for c in other_configs:
            if c['source_period'] != '1m':
                print(f"      - {c['target_period']}: {c['source_period']}")

if __name__ == "__main__":
    test_config_generation()
