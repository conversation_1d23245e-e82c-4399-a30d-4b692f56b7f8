#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试merge_dataframes函数修复结果

验证修复后的数据合并功能是否正常工作
"""

import sys
import os
import pandas as pd
import datetime
from typing import Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入修复后的函数
from utils.data_processor.data_merger import merge_dataframes
from utils.logger import get_unified_logger

# 初始化日志
logger = get_unified_logger(__name__, enhanced=True)


def create_test_data_with_timestamps() -> tuple[pd.DataFrame, pd.DataFrame]:
    """创建包含时间戳的测试数据"""
    # 创建旧数据 - 使用Timestamp类型
    old_data = pd.DataFrame({
        'time': [
            pd.Timestamp('2025-01-27 08:00:00'),
            pd.Timestamp('2025-01-27 08:01:00'),
            pd.Timestamp('2025-01-27 08:02:00')
        ],
        'price': [100.0, 101.0, 102.0],
        'volume': [1000, 1100, 1200]
    })
    
    # 创建新数据 - 使用Timestamp类型，有重叠
    new_data = pd.DataFrame({
        'time': [
            pd.Timestamp('2025-01-27 08:02:00'),  # 重叠
            pd.Timestamp('2025-01-27 08:03:00'),
            pd.Timestamp('2025-01-27 08:04:00')
        ],
        'price': [102.5, 103.0, 104.0],
        'volume': [1250, 1300, 1400]
    })
    
    return old_data, new_data


def create_test_data_with_mixed_types() -> tuple[pd.DataFrame, pd.DataFrame]:
    """创建混合类型的测试数据"""
    # 创建旧数据 - 使用Timestamp类型
    old_data = pd.DataFrame({
        'time': [
            pd.Timestamp('2025-01-27 08:00:00'),
            pd.Timestamp('2025-01-27 08:01:00')
        ],
        'price': [100.0, 101.0],
        'volume': [1000, 1100]
    })
    
    # 创建新数据 - 使用毫秒时间戳
    new_data = pd.DataFrame({
        'time': [1737158520000, 1737158580000],  # 对应08:02:00和08:03:00
        'price': [102.0, 103.0],
        'volume': [1200, 1300]
    })
    
    return old_data, new_data


def test_basic_merge():
    """测试基本的数据合并功能"""
    logger.info("开始测试基本数据合并功能")
    
    try:
        old_data, new_data = create_test_data_with_timestamps()
        
        # 测试合并
        result = merge_dataframes(old_data, new_data, force_update=True)
        
        # 验证结果
        assert result is not None, "合并结果不应为空"
        assert len(result) > 0, "合并结果应包含数据"
        
        logger.info(f"基本合并测试通过 - 合并后数据: {len(result)} 行")
        logger.info(f"合并结果列: {list(result.columns)}")
        
        return True
        
    except Exception as e:
        logger.error(f"基本合并测试失败: {e}")
        return False


def test_mixed_type_merge():
    """测试混合类型数据合并"""
    logger.info("开始测试混合类型数据合并")
    
    try:
        old_data, new_data = create_test_data_with_mixed_types()
        
        # 测试合并
        result = merge_dataframes(old_data, new_data, force_update=False)
        
        # 验证结果
        assert result is not None, "合并结果不应为空"
        assert len(result) > 0, "合并结果应包含数据"
        
        logger.info(f"混合类型合并测试通过 - 合并后数据: {len(result)} 行")
        
        return True
        
    except Exception as e:
        logger.error(f"混合类型合并测试失败: {e}")
        return False


def test_empty_data_merge():
    """测试空数据合并"""
    logger.info("开始测试空数据合并")
    
    try:
        # 测试空旧数据
        old_data = pd.DataFrame()
        new_data = pd.DataFrame({
            'time': [pd.Timestamp('2025-01-27 08:00:00')],
            'price': [100.0],
            'volume': [1000]
        })
        
        result = merge_dataframes(old_data, new_data)
        assert result is not None and len(result) == 1, "空旧数据合并失败"
        
        # 测试空新数据
        old_data = pd.DataFrame({
            'time': [pd.Timestamp('2025-01-27 08:00:00')],
            'price': [100.0],
            'volume': [1000]
        })
        new_data = pd.DataFrame()
        
        result = merge_dataframes(old_data, new_data)
        assert result is not None and len(result) == 1, "空新数据合并失败"
        
        logger.info("空数据合并测试通过")
        return True
        
    except Exception as e:
        logger.error(f"空数据合并测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    logger.info("=" * 50)
    logger.info("开始运行merge_dataframes修复验证测试")
    logger.info("=" * 50)
    
    tests = [
        ("基本合并功能", test_basic_merge),
        ("混合类型合并", test_mixed_type_merge),
        ("空数据合并", test_empty_data_merge)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n运行测试: {test_name}")
        if test_func():
            logger.info(f"✅ {test_name} - 通过")
            passed += 1
        else:
            logger.error(f"❌ {test_name} - 失败")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"测试结果: {passed}/{total} 通过")
    logger.info("=" * 50)
    
    if passed == total:
        logger.info("🎉 所有测试通过！merge_dataframes修复成功")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
